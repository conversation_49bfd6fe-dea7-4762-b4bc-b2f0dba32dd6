package cn.jwis.product.pdm.customer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.jwis.framework.base.bean.util.BeanUtil;
import cn.jwis.framework.base.domain.able.info.ModelInfo;
import cn.jwis.framework.base.dto.UserDTO;
import cn.jwis.framework.base.exception.JWIException;
import cn.jwis.framework.base.util.Assert;
import cn.jwis.framework.base.util.CollectionUtil;
import cn.jwis.framework.base.util.OidGenerator;
import cn.jwis.framework.base.util.SpringContextUtil;
import cn.jwis.framework.base.web.session.SessionHelper;
import cn.jwis.framework.database.core.entity.EntityFilter;
import cn.jwis.framework.database.core.query.dynamic.Condition;
import cn.jwis.platform.iam.user.User;
import cn.jwis.platform.plm.account.auth.service.AuthHelper;
import cn.jwis.platform.plm.account.entity.tenant.Tenant;
import cn.jwis.platform.plm.account.tenant.service.TenantService;
import cn.jwis.platform.plm.account.user.service.UserHelper;
import cn.jwis.platform.plm.dataengine.common.JWICommonService;
import cn.jwis.platform.plm.foundation.common.dto.RelatedFuzzyDTO;
import cn.jwis.platform.plm.foundation.common.response.InstanceEntity;
import cn.jwis.platform.plm.foundation.common.service.CommonAbilityHelper;
import cn.jwis.platform.plm.foundation.common.service.InstanceHelper;
import cn.jwis.platform.plm.foundation.lifecycle.able.LifecycleAble;
import cn.jwis.platform.plm.foundation.relationship.enums.RelationConstraint;
import cn.jwis.platform.plm.permission.util.PermissionCacheUtil;
import cn.jwis.platform.plm.sysconfig.collectionrule.service.CollectionRuleHelper;
import cn.jwis.platform.plm.sysconfig.entity.CollectionRule;
import cn.jwis.platform.plm.workflow.engine.order.entity.ProcessOrder;
import cn.jwis.platform.plm.workflow.engine.service.interf.ProcessOrderHelper;
import cn.jwis.product.pdm.cad.ecad.dto.SyncToAccessDTO;
import cn.jwis.product.pdm.cad.ecad.entity.ECADIteration;
import cn.jwis.product.pdm.cad.ecad.service.EdaIntegrationHelper;
import cn.jwis.product.pdm.cad.mcad.entity.MCADIteration;
import cn.jwis.product.pdm.change.entity.ECA;
import cn.jwis.product.pdm.change.entity.ECR;
import cn.jwis.product.pdm.change.response.ChangeInfo;
import cn.jwis.product.pdm.change.service.ECAServiceImpl;
import cn.jwis.product.pdm.customer.entity.DingTaskRecord;
import cn.jwis.product.pdm.customer.entity.PermApplyEntity;
import cn.jwis.product.pdm.customer.entity.assistant.CheckedAssist;
import cn.jwis.product.pdm.customer.entity.assistant.SafeWrapAssist;
import cn.jwis.product.pdm.customer.repo.CustomerCommonRepo;
import cn.jwis.product.pdm.customer.repo.IntegrationMonitorRepo;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendERP;
import cn.jwis.product.pdm.customer.service.delegate.BatchSendHZZ;
import cn.jwis.product.pdm.customer.service.dto.GetProcessInstanceResponseBodyResultTasks;
import cn.jwis.product.pdm.customer.service.dto.WorkflowActivityRules;
import cn.jwis.product.pdm.customer.service.interf.CommonService;
import cn.jwis.product.pdm.customer.service.interf.CustomerChangeHelper;
import cn.jwis.product.pdm.customer.service.interf.CustomerDocumentHelper;
import cn.jwis.product.pdm.customer.service.interf.DingTalkService;
import cn.jwis.product.pdm.customer.service.release.DocReleaseIDS;
import cn.jwis.product.pdm.customer.service.release.EntityReleaseFactory;
import cn.jwis.product.pdm.customer.service.release.PMSRelease;
import cn.jwis.product.pdm.customer.service.release.PartEntityRelease;
import cn.jwis.product.pdm.customer.service.util.DingTalkHelper;
import cn.jwis.product.pdm.customer.service.util.HttpAssistant;
import cn.jwis.product.pdm.document.dto.DocSignWorkflowDTO;
import cn.jwis.product.pdm.document.dto.DwgSignWorkflowDTO;
import cn.jwis.product.pdm.document.entity.DocumentIteration;
import cn.jwis.product.pdm.document.service.DocumentHelper;
import cn.jwis.product.pdm.partbom.part.dto.SimplePartBOMNode;
import cn.jwis.product.pdm.partbom.part.entity.PartIteration;
import cn.jwis.product.pdm.partbom.part.service.PartHelper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkworkflow_1_0.Client;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import com.rabbitmq.client.Channel;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DingTalkCallListen implements SafeWrapAssist, CheckedAssist {

    public static final ExecutorService DING_TASK_COMPLETE_THREAD_POOL = Executors.newFixedThreadPool(10);

    @Autowired
    private AuthHelper authHelper;

    @Autowired
    private CommonService commonService;

    @Autowired
    PartHelper partHelper;

    @Autowired
    PartEntityRelease partEntityRelease;

    @Autowired
    private DocReleaseIDS docReleaseIDS;

    @Autowired
    TenantService tenantService;

    @Autowired
    private CustomerChangeHelper customerChangeHelper;

    @Autowired
    JWICommonService jwiCommonService;

    @Autowired
    private CustomerCommonRepo customerCommonRepo;

    @Resource
    private UserHelper userHelper;

    @Autowired
    private DocumentHelper documentHelper;

    @Resource
    EdaIntegrationHelper edaIntegrationHelper;

    @Autowired
    DingTalkService dingTalkService;

    @Autowired
    CustomerDocumentHelper customerDocumentHelper;

    @Value("${dingTalk.appKey}")
    public String dingTalkAppKey;
    @Value("${dingTalk.appSecret}")
    public String dingTalkAppSecret;
    @Value("${dingTalk.agentId}")
    public Long dingTalkAgentId;

    // 应用名：PDM  用于与PDM的文件外发和技术文档发布
    @Value("${dingTalk.appKeyNew:${dingTalk.appKey}}")
    public String dingTalkAppKeyNew;
    @Value("${dingTalk.appSecretNew:${dingTalk.appSecret}}")
    public String dingTalkAppSecretNew;
    @Value("${dingTalk.agentIdNew:${dingTalk.agentId}}")
    public Long dingTalkAgentIdNew;

    @Value("${dingTalk.processCodeflow:true}")
    private boolean dingTalkProcessCodeflow;

    @Value("${dingTalk.processCode}")
    public String dingTalkProcessCode;

    @Value("${dingTalk.processCodeJSDoc:jishucode}")
    private String dingTalkprocessCodeJSDoc;

    @Value("${dingTalk.processCodeWuliao:PROC-B94E866D-1902-415E-B11A-8B870CE1B076}")
    private String wuliaoProcessCode;

    @Value("${dingTalk.materialSwitch:false}")
    private boolean materialSwitch;

    @Value("${dingTalk.perm.processCode:PROC-1FE7FBEB-5CAC-42DC-88E9-DEBD14678102}")
    public String permProcessCode;

    @Value("${dingTalk.processCodeBOMRelease:PROC-B8731E81-A0AC-413A-A640-8FA3824AFABF}")
    private String processCodeBOMRelease;

    @Value("${dingTalk.processCodeSoftWareConfigCreate:PROC-5DF156D7-98AE-4EC5-9C13-33B172042547}")
    private String processCodeSoftWareConfigCreate;

    @Value("${dingTalk.processCodeSoftWareConfigChange:PROC-213AAB07-9BAC-47A7-921B-C2D783CB2431}")
    private String processCodeSoftWareConfigChange;

    @Value("${dingTalk.processCodeDoc:PROC-84CA0DB2-3693-4AAA-8060-31D289E24866}")
    private String processCodeFileOutSend;

    @Value("${perm.use.test:false}")
    private Boolean permUseTest;

    @Autowired
    private PermissionCacheUtil permissionCacheUtil;

    @Resource
    RedisTemplate redisTemplate;

    @Autowired
    private CollectionRuleHelper collectionRuleHelper;

    @Autowired
    ProcessOrderHelper processOrderHelper;

    @Autowired
    DingTalkHelper dingTalkHelper;

    @Autowired
    BatchSendHZZ batchSendHZZ;
    @Autowired
    PMSRelease pmsRelease;

    private final Map<String, String> ddRoleNameAlsoInPdm_pdmSignCodeMap = new HashMap<String, String>() {
        {
            put("标准化", "cn_jwis_bzh");
            put("产保", "cn_jwis_bzh");
            put("校对", "cn_jwis_proofreader");
            put("批准", "cn_jwis_ratifier");
            put("审核", "cn_jwis_shz");
            put("会签", "cn_jwis_countersigner");
        }
    };

    private final Map<String, String> ecadSignCodeNameMap = new HashMap<String, String>() {
        {
            put("cn_jwis_bzh", "标准化");
            put("cn_jwis_proofreader", "校对");
            put("cn_jwis_ratifier", "批准");
            put("cn_jwis_shz", "审核者");
            put("cs", "会签");
        }
    };

    private final Map<String, String> pdfSignCodeNameMap = new HashMap<String, String>() {
        {
            put("cn_jwis_bzh", "标准化");
            put("cn_jwis_proofreader", "校对");
            put("cn_jwis_ratifier", "批准");
            put("cn_jwis_shz", "审核");
            put("cs", "会签");
        }
    };

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy/M/dd");

    public EventAckStatus listening(GenericOpenDingTalkEvent event){
        DingTaskRecord dingTaskRecord = null;
        try {
            JSONObject bizData = event.getData();
            log.info("钉钉stream返回消息bizData：" + bizData);
            // bpms_instance_change
            String processCode = bizData.getString("processCode");
            String type = bizData.getString("type");
            String result = bizData.getString("result");
            //获取事件体
            String eventType = event.getEventType();
            String activityName = bizData.getString("activityName");
            log.info("钉钉返回消息eventType：" + eventType);

            if (eventType.equals("bpms_instance_change") && dingTalkProcessCodeflow && processCode.equals(dingTalkprocessCodeJSDoc) && !"start".equals(type)) {
                String processInstanceId = bizData.getString("processInstanceId");

                dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where("dingProcessInstanceId").eq(processInstanceId), DingTaskRecord.class); // 查询钉钉流程记录表数据
                Assert.notNull(dingTaskRecord, "无钉钉流程记录processInstanceId:" + processInstanceId);
                log.info("查询到dingTaskRecord==>" + JSONUtil.toJsonStr(dingTaskRecord));
                dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                dingTaskRecord.setDingType(type);

                // 变更流程
                if (dingTaskRecord.getIsChange() != null && dingTaskRecord.getIsChange() && !CollectionUtils.isEmpty(dingTaskRecord.getEcrOidList())) {
                    log.info("变更ecrOid==>" + dingTaskRecord.getEcrOidList());
                    dingTaskRecord.setResult(result);
                    jwiCommonService.update(dingTaskRecord);
                    if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                        if(StringUtils.isBlank(result)) {
                            dingTaskRecord.setResult("terminate");
                        }
                        customerCommonRepo.updateLifeStatus(ECR.TYPE, dingTaskRecord.getEcrOidList(), "Closed");
                    }
                    if ("finish".equals(type) && "agree".equals(result)) {
                        customerCommonRepo.updateLifeStatus(ECR.TYPE, dingTaskRecord.getEcrOidList(), "Released");
                        dingTaskRecord.getEcrOidList().forEach(ecrOid -> customerChangeHelper.closeIssue(ecrOid));
                        signDocByDingTalk(dingTaskRecord);
                    }
                    jwiCommonService.update(dingTaskRecord);
                    return EventAckStatus.SUCCESS;
                } else if(dingTaskRecord.getIsEcad() != null && dingTaskRecord.getIsEcad()){
                    adEcadSign(dingTaskRecord, result, type);
                    return EventAckStatus.SUCCESS;
                } else {
                    dingTaskRecord.setResult(result);
                    //钉钉撤销审批
                    if ("terminate".equals(type)) {
                        customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Design");
                        jwiCommonService.update(dingTaskRecord);
                        clearCache(dingTaskRecord);
                        return EventAckStatus.SUCCESS;
                    }
                    if ("finish".equals(type) && "refuse".equals(result)) { //钉钉拒绝审批
                        jwiCommonService.update(dingTaskRecord);
                        customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Design");
                        clearCache(dingTaskRecord);
                        return EventAckStatus.SUCCESS;
                    }

                    if ("finish".equals(type) && "agree".equals(result)){ //钉钉流程结束状态
                        List<DocumentIteration> byOidList = jwiCommonService.findByOid(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), DocumentIteration.class);
                        for(DocumentIteration byOid:byOidList){
                            boolean latest = byOid.isLatest();
                            if(latest == false){
                                return EventAckStatus.SUCCESS;
                            }
                        }
                        jwiCommonService.update(dingTaskRecord);
                        customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Released");
                        signDocByDingTalk(dingTaskRecord);
                        clearCache(dingTaskRecord);
                    }
                    return EventAckStatus.SUCCESS;
                }
            }
            else if(materialSwitch && wuliaoProcessCode.equals(processCode)) {
                String processInstanceId = bizData.getString("processInstanceId");
                dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where("dingProcessInstanceId").eq(processInstanceId), DingTaskRecord.class); // 查询钉钉流程记录表数据
                Assert.notNull(dingTaskRecord, "无钉钉流程记录processInstanceId:" + processInstanceId);
                log.info("物料申请流程 查询到dingTaskRecord==>" + JSONUtil.toJsonStr(dingTaskRecord));


                if(eventType.equals("bpms_instance_change")) {
                    String eventTypeKey = processInstanceId + "_" + eventType + "_" + type + "_" + result;
                    String redisKey = "DINGDING:CALLBACK:" + eventTypeKey;
                    Boolean isFirstCallback = redisTemplate.opsForValue().setIfAbsent(redisKey, "1", 10, TimeUnit.MINUTES);
                    if (Boolean.FALSE.equals(isFirstCallback)) {
                        log.info("bpms_instance_change Callback already processed for key: " + redisKey);
                        return EventAckStatus.SUCCESS;
                    }
                    dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                    dingTaskRecord.setDingType(type);
                    dingTaskRecord.setResult(result);
                    jwiCommonService.update(dingTaskRecord);

                    if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                        customerCommonRepo.updateLifeStatus(PartIteration.TYPE, dingTaskRecord.getPartInterationOidList(),
                                "Draft");
                        clearCache(dingTaskRecord);
                        return EventAckStatus.SUCCESS;
                    }
                    if ("finish".equals(type) && "agree".equals(result)) {
                        dingTalkService.uploadDINGFormObj(processInstanceId,dingTaskRecord);
                        customerCommonRepo.updateLifeStatus(PartIteration.TYPE, dingTaskRecord.getPartInterationOidList(), "Released");
                        //流程结束后，将所有相关对象的状态全部设置为已发布
                        setRelationObjectLifecycleStatus(dingTaskRecord);
                        return EventAckStatus.SUCCESS;
                    }

                }
                else if (eventType.equals("bpms_task_change")) {
                    String eventTypeKey = processInstanceId + "_" + eventType + "_" + type + "_" + result + "_" + activityName;
                    String redisKey = "DINGDING:CALLBACK:" + eventTypeKey;
                    Boolean isFirstCallback = redisTemplate.opsForValue().setIfAbsent(redisKey, "1", 10, TimeUnit.MINUTES);
                    if (Boolean.FALSE.equals(isFirstCallback)) {
                        log.info("bpms_task_change Callback already processed for key: " + redisKey);
                        return EventAckStatus.SUCCESS;
                    }
                    dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                    dingTaskRecord.setDingType(type);
                    dingTaskRecord.setResult(result);
                    jwiCommonService.update(dingTaskRecord);
                    //获取审批实例详情
                    if ("发送数据到U9".equals(activityName) && "finish".equals(type) && "agree".equals(result)) {
                        //发送本次的物料
                        UserDTO byAccount = userHelper.findByAccount(dingTaskRecord.getCreateBy());
                        SessionHelper.addCurrentUser(byAccount);
//                        partSyncAndSendErp(dingTaskRecord);
                        List<InstanceEntity> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                                dingTaskRecord.getPartInterationOidList(),InstanceEntity.class);
                        this.batchSendERP.batchSendERP(instanceList, Boolean.FALSE);
                        customerCommonRepo.updateLifeStatus(PartIteration.TYPE, dingTaskRecord.getPartInterationOidList(), "Design");
                        return EventAckStatus.SUCCESS;
                    }
                    else if ("增加值集值".equals(activityName) &&"finish".equals(type) && "agree".equals(result)) {
                        // 流程结束后，临时编码的要重新生成编码
                        rebuildNumber(dingTaskRecord);
                        customerCommonRepo.updateLifeStatus(PartIteration.TYPE, dingTaskRecord.getPartInterationOidList(), "Design");
                    }
                    else if ("上传原理图".equals(activityName) &&"finish".equals(type) && "agree".equals(result)) {
                        customerCommonRepo.updateLifeStatus(PartIteration.TYPE, dingTaskRecord.getPartInterationOidList(), "Design");
                    }
                }
            }
            else if (dingTalkProcessCode.equals(processCode) && type.equals("finish")) {
                DING_TASK_COMPLETE_THREAD_POOL.execute(new Runnable() {
                    public void run() {
                        dingTalkService.autoCompleteTask(bizData);
                    }
                });
            }
            else if (permProcessCode.equals(processCode) && !type.equals("start")) {
                log.info("perm call==>" + JSONUtil.toJsonStr(bizData));
                String instanceId = bizData.getString("processInstanceId");

                EntityFilter filter = new EntityFilter();
                filter.setType(PermApplyEntity.TYPE);
                filter.setFilter(Condition.where("oid").eq(instanceId));
                PermApplyEntity record = (PermApplyEntity) CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter));
                if(record == null)
                    throw new JWIException("未找到权限申请记录==>" + JSONUtil.toJsonStr(bizData));

                record.setUpdateDate(System.currentTimeMillis());
                UserDTO userDTO = new UserDTO();
                userDTO.setAccount(record.getCreateBy());
                userDTO.setTenantOid(record.getTenantOid());
//                userDTO.setTenantOid("6deb5dde-aa39-46fb-962d-a5951f8fab5e");
                SessionHelper.addCurrentUser(userDTO);
                if ("terminate".equals(type)) {
                    record.setStatus("撤销");
                    commonAbilityHelper.doUpdate(record);
                    return EventAckStatus.SUCCESS;
                }

                if(("finish".equals(type) && "refuse".equals(result))){
                    record.setStatus("拒绝");
                    commonAbilityHelper.doUpdate(record);
                    return EventAckStatus.SUCCESS;
                }

                Config config = new Config(); config.protocol = "https"; config.regionId = "central"; String accessToken = permUseTest ? dingTalkService.getToken() : dingTalkService.getTokenNew();
                GetProcessInstanceHeaders getProcessInstanceHeaders = new GetProcessInstanceHeaders();
                getProcessInstanceHeaders.xAcsDingtalkAccessToken = accessToken;
                Client client = new Client(config);
                GetProcessInstanceRequest getProcessInstanceRequest = new GetProcessInstanceRequest().setProcessInstanceId(instanceId);
                GetProcessInstanceResponse instanceResult = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new RuntimeOptions());
                GetProcessInstanceResponseBody body = instanceResult.getBody();
                log.info("result body==>" + JSONUtil.toJsonStr(body));
                String status = body.getResult().getStatus();

                if ("finish".equals(type) && "agree".equals(result) && "COMPLETED".equals(status))
                    permApplyFinish(bizData, record, accessToken);

            }
            return EventAckStatus.SUCCESS;

        } catch (Exception e) {
            //消费失败
            log.error(e.getMessage(), e);
            if(ObjectUtils.isNotEmpty(dingTaskRecord)) {
                saveException(e,dingTaskRecord);
            }
            return EventAckStatus.LATER;
        }
    }

    /**
     * 流程结束，对临时流水号重新生成正常的流水号
     * @param dingTaskRecord
     */
    private void rebuildNumber(DingTaskRecord dingTaskRecord) {
        // 查找用户信息
        UserDTO byAccount = this.userHelper.findByAccount(dingTaskRecord.getCreateBy());

        // 查找租户信息
        List<Tenant> tenants = this.tenantService.searchByUserOid(byAccount.getOid());

        if (CollectionUtil.isNotEmpty(tenants)) {
            byAccount.setTenantOid(tenants.get(0).getOid());
        }

        // 设置当前用户上下文
        SessionHelper.addCurrentUser(byAccount);
        SessionHelper.setAppId("pdm");

        // 获取实例实体
        List<PartIteration> instanceList = commonAbilityHelper.findDetailEntity(dingTaskRecord.getPartInterationOidList(), PartIteration.TYPE)
                .stream().map(item -> (PartIteration)item).collect(Collectors.toList());
        log.info("获取到 {} 个 PartIteration 实例：{}", instanceList.size(), instanceList);

        // 执行更新逻辑
        for (PartIteration instanceEntity : instanceList) {
            String number = instanceEntity.getNumber();
            log.info("处理 PartIteration 实例，OID: {}, 编码: {}", instanceEntity.getOid(), number);

            if (null != number && number.contains("LS")) {
                log.info("编号包含 'LS'，执行更新操作，PartIteration: {}", instanceEntity);
                commonAbilityHelper.doUpdate(instanceEntity);
            } else {
                log.info("编号不包含 'LS'，跳过该实例");
            }
        }
    }

    /**
     * 流程结束设置物料相关对象所有状态为 已发布
     * @param dingTaskRecord
     */
    private void setRelationObjectLifecycleStatus(DingTaskRecord dingTaskRecord) {
        List<CollectionRule> materialRuleList =  collectionRuleHelper.findByAppliedType("JWIRawMaterial_Related_Object", "JWIRawMaterial");
        SessionHelper.addCurrentUser(createUserDTO(dingTaskRecord));
        for (String oid : dingTaskRecord.getPartInterationOidList()) {
            for (CollectionRule rule : materialRuleList) {
                RelatedFuzzyDTO relatedFuzzyDTO = createRelatedFuzzyDTO(rule, oid);

                List<InstanceEntity> relateObjList = instanceHelper.fuzzyRelated(relatedFuzzyDTO);

                if (!relateObjList.isEmpty()) {
                    InstanceEntity instanceEntity = relateObjList.get(0);
                    String rawType = instanceEntity.getType();
                    // 过滤状态不是 "Released" 的 InstanceEntity
                    List<String> objects = relateObjList.stream()
                            .filter(entity -> !"Released".equals(entity.getLifecycleStatus()))  // 过滤条件
                            .map(InstanceEntity::getOid)
                            .collect(Collectors.toList());
                    log.info("当前需要发布的相关对象oid集合为--->>>>>" + objects.toString());
                    customerCommonRepo.updateLifeStatus(rawType, objects, "Released");
                }
            }
        }
    }

    /**
     * 清空
     * @param dingTaskRecord
     */
    private void clearCache(DingTaskRecord dingTaskRecord){
        String createBy = dingTaskRecord.getCreateBy();
        UserDTO user = userHelper.findByAccount(createBy);
        if(user != null){
            SessionHelper.addCurrentUser(user);
            permissionCacheUtil.clearCatch();
        }
    }

    private void partSyncAndSendErp(DingTaskRecord dingTaskRecord) {
        SyncToAccessDTO dto = new SyncToAccessDTO();
        dto.setOids(dingTaskRecord.getPartInterationOidList());
        this.edaIntegrationHelper.saveBatch(dto);
        List<InstanceEntity> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                dingTaskRecord.getPartInterationOidList(),InstanceEntity.class);
        this.batchSendERP.batchSendERP(instanceList, Boolean.FALSE);
    }

    @Value("${prod.env:false}")
    private Boolean prodEnv;

    public void listeningMq(Message message, Channel channel){
        DingTaskRecord dingTaskRecord = null;
        try {
            String bodyStr = new String(message.getBody(), "UTF-8");
            String uuid = UUID.randomUUID().toString();
            //获取事件体
            if(bodyStr.startsWith("{")){
                log.info(uuid + " bpms_instance_pdm messge body info: " + bodyStr);
                JSONObject bizData = JSON.parseObject(bodyStr);

                String processCode = bizData.getString("processCode");
//                String instanceId = bizData.getString("instanceId");
                String eventType = bizData.getString("EventType");
                String type = bizData.getString("type");
                String processInstanceId = bizData.getString("processInstanceId");
                String result = bizData.getString("result");

                String actiName = bizData.containsKey("activityName") ? bizData.getString("activityName") : "N/A";

                log.info("接收到钉钉流程回调信息：processCode={}, processInstanceId={}, eventType={}, activityName={}, type={}, result={}", processCode, processInstanceId, eventType, actiName, type, result);

                if (dingTalkProcessCodeflow && processCode.equals(dingTalkprocessCodeJSDoc)
                        && (prodEnv && "PROC-C676A6BE-52D3-409F-8906-3538AB6547A0".equals(dingTalkprocessCodeJSDoc) // 正式环境回调
                            || !prodEnv && "PROC-8F4E5D1B-01C0-4C1B-A106-286132D18BF4".equals(dingTalkprocessCodeJSDoc))) {// 正式环境备份回调
                    log.info("技术文档流程回调监听 processInstanceId==>" + processInstanceId + " type==>" + type);
                    if(!"start".equals(type)){
                        // 查询钉钉流程记录表数据
                        dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where("dingProcessInstanceId").eq(processInstanceId), DingTaskRecord.class);
                        log.info(" dingTaskRecord data {}", dingTaskRecord);
                        Assert.notNull(dingTaskRecord, "无钉钉流程记录processInstanceId:" + processInstanceId);
                        dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                        dingTaskRecord.setDingType(type);
                        String activityName = bizData.getString("activityName");
                        String activityId = bizData.getString("activityId");
                        String taskId = bizData.getString("taskId");
                        String hanshenName = "【函审组组长】确认问题闭环";
                        String pizhunName = "批准";
                        Boolean isTaskComplete = Boolean.TRUE.equals(dingTaskRecord.getIsTaskComplete());
                        if (dingTaskRecord.getIsChange() != null && dingTaskRecord.getIsChange() && !CollectionUtils.isEmpty(dingTaskRecord.getEcrOidList())) {
                            log.info("变更ecrOid==>" + dingTaskRecord.getEcrOidList());
                            dingTaskRecord.setResult(result);
                            if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                                if(StringUtils.isBlank(result)) {
                                    dingTaskRecord.setResult("terminate");
                                }
                                customerCommonRepo.updateLifeStatus(ECR.TYPE,
                                        dingTaskRecord.getEcrOidList(), "Closed");
                            }
                            jwiCommonService.update(dingTaskRecord);
                            if (eventType.equals("bpms_task_change")) {
                                if ((pizhunName.equals(activityName) || hanshenName.equals(activityName)) && "finish".equals(type) && "agree".equals(result)&& !isTaskComplete) {
                                    boolean isAllCompleted = getIsAllCompleted(processInstanceId, activityId, taskId);
                                    if (isAllCompleted) {
                                        customerCommonRepo.updateLifeStatus(ECR.TYPE, dingTaskRecord.getEcrOidList(), "Released");
                                        dingTaskRecord.getEcrOidList().forEach(ecrOid -> customerChangeHelper.closeIssue(ecrOid));
                                        signDocByDingTalk(dingTaskRecord);
                                        dingTaskRecord.setIsTaskComplete(Boolean.TRUE);
                                        jwiCommonService.update(dingTaskRecord);
                                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                                        return;
                                    }
                                }
                            }
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        } else if(dingTaskRecord.getIsEcad() != null && dingTaskRecord.getIsEcad()){
                            if (eventType.equals("bpms_task_change")) {
                                if ((pizhunName.equals(activityName) || hanshenName.equals(activityName)) && "finish".equals(type) && "agree".equals(result) && !isTaskComplete) {
                                    boolean isAllCompleted = getIsAllCompleted(processInstanceId, activityId, taskId);
                                    if (isAllCompleted) {
                                        adEcadSign(dingTaskRecord, result, type);
                                        dingTaskRecord.setIsTaskComplete(Boolean.TRUE);
                                        jwiCommonService.update(dingTaskRecord);
                                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                                        return;
                                    }
                                }
                            }
                        } else { // 技术文档发布流程
                            log.info("技术文档发布流程instanceId：{} type:{} result:{}", processInstanceId, type, result);
                            if(eventType.equals("bpms_instance_change")) {
                                //钉钉撤销审批
                                if ("terminate".equals(type)) {
                                    dingTaskRecord.setResult(type);
                                    log.info("流程撤销更新状态为 撤销 instanceId:{}", processInstanceId);
                                    //更新审批对象状态生命周期
                                    customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Design");
                                    jwiCommonService.update(dingTaskRecord);
                                    //IDS 状态取消
//                                    releaseIDSWithStatus(dingTaskRecord.getDocumentIterationOidList(), 1);
                                    clearCache(dingTaskRecord);
                                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                                    return;
                                }
                                //钉钉拒绝审批
                                if ("finish".equals(type) && "refuse".equals(result)) {
                                    log.info("流程状态为 拒绝 instanceId:{}", processInstanceId);
                                    dingTaskRecord.setResult(result);
                                    jwiCommonService.update(dingTaskRecord);
                                    //更新审批对象状态生命周期
                                    customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Design");
                                    //IDS 状态取消
//                                    releaseIDSWithStatus(dingTaskRecord.getDocumentIterationOidList(), 1);
                                    clearCache(dingTaskRecord);
                                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                                    return;
                                }
                            }
                            else if (eventType.equals("bpms_task_change")) {
                                //钉钉流程结束状态
                                log.info("钉钉返回消息eventType：" + eventType);
                                log.info("流程状态为 同意 activityName:{}", activityName);
                                if ((pizhunName.equals(activityName) || hanshenName.equals(activityName)) && "finish".equals(type) && "agree".equals(result) && !isTaskComplete) {
                                    // 适配钉钉流程中的 批准节点会签，所有人审批通过视为通过
                                    boolean isAllCompleted = getIsAllCompleted(processInstanceId, activityId, taskId);
                                    if (isAllCompleted) {
                                        dingTaskRecord.setIsTaskComplete(Boolean.TRUE);
                                        log.info("流程状态为 同意 instanceId:{}", processInstanceId);
                                        List<DocumentIteration> byOidList = jwiCommonService.findByOid(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), DocumentIteration.class);
                                        for (DocumentIteration byOid : byOidList) {
                                            boolean latest = byOid.isLatest();
                                            if (latest == false) {
                                                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                                                return;
                                            }
                                        }
                                        dingTaskRecord.setResult(result);
                                        jwiCommonService.update(dingTaskRecord);
                                        customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Released");

                                        signDocByDingTalk(dingTaskRecord);
//                                        releaseIDSWithStatus(dingTaskRecord.getDocumentIterationOidList(), 0);
//                                        releasePMS(dingTaskRecord); //只在技术文档发布流程中推送数据到PMS
                                        clearCache(dingTaskRecord);
                                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                                        return;
                                    }
                                }
                            }
                        }
                    }
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    return;
                }
                else if(materialSwitch && wuliaoProcessCode.equals(processCode)
                        && (prodEnv && "PROC-3ADD3F5C-CC1C-4AC2-85EE-5E5961141A1F".equals(wuliaoProcessCode) // 正式环境回调
                        || !prodEnv && "PROC-3C85B567-39BB-422A-BBDA-61889AA034BD".equals(wuliaoProcessCode))) {// 正式环境 物料测试流程
                    if("start".equals(type)){
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                    String activityName = bizData.getString("activityName");

                    dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where("dingProcessInstanceId").eq(processInstanceId), DingTaskRecord.class); // 查询钉钉流程记录表数据
                    Assert.notNull(dingTaskRecord, "无钉钉流程记录processInstanceId:" + processInstanceId);
                    log.info("物料申请流程 查询到dingTaskRecord==>" + JSONUtil.toJsonStr(dingTaskRecord));

                    if(eventType.equals("bpms_instance_change")) {
                        String eventTypeKey = processInstanceId + "_" + eventType + "_" + type + "_" + result;
                        String redisKey = "DINGDING:CALLBACK:" + eventTypeKey;
                        Boolean isFirstCallback = redisTemplate.opsForValue().setIfAbsent(redisKey, "1", 10, TimeUnit.MINUTES);
                        if (Boolean.FALSE.equals(isFirstCallback)) {
                            log.info("bpms_instance_change Callback already processed for key: " + redisKey);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                        dingTaskRecord.setDingType(type);
                        dingTaskRecord.setResult(result);
                        jwiCommonService.update(dingTaskRecord);

                        if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                            // 1. 获取所有实例
                            List<InstanceEntity> instancePartList = jwiCommonService.findByOid(
                                    PartIteration.TYPE,
                                    dingTaskRecord.getPartInterationOidList(),
                                    InstanceEntity.class
                            );

                            // 2. 过滤出生命周期状态为“审阅中”的实例
                            List<String> toUpdateOidList = instancePartList.stream()
                                    .filter(instance -> "UnderReview".equals(instance.getLifecycleStatus()))
                                    .map(InstanceEntity::getOid)
                                    .collect(Collectors.toList());

                            // 3. 如果有需要修改状态的，更新为草稿
                            if (!toUpdateOidList.isEmpty()) {
                                customerCommonRepo.updateLifeStatus(PartIteration.TYPE, toUpdateOidList, "Draft");
                            }

                            // 4. 清除缓存 & 消息确认
                            clearCache(dingTaskRecord);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        if ("finish".equals(type) && "agree".equals(result)) {

                            //根据是否有BOM来确定 设置的状态：有BOM-设计中  无BOM-已发布
//                            setPartLifeStatusByBom(dingTaskRecord);
//                            setPartLifeStatus(dingTaskRecord);
                            //流程结束后，将所有相关对象的状态全部设置为已发布
//                            setRelationObjectLifecycleStatus(dingTaskRecord);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }

                    }
                    else if (eventType.equals("bpms_task_change")) {
                        String eventTypeKey = processInstanceId + "_" + eventType + "_" + type + "_" + result + "_" + activityName;
                        String redisKey = "DINGDING:CALLBACK:" + eventTypeKey;
                        Boolean isFirstCallback = redisTemplate.opsForValue().setIfAbsent(redisKey, "1", 10, TimeUnit.MINUTES);
                        if (Boolean.FALSE.equals(isFirstCallback)) {
                            log.info("bpms_task_change Callback already processed for key: " + redisKey);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                        dingTaskRecord.setDingType(type);
//                        dingTaskRecord.setResult(result);
                        jwiCommonService.update(dingTaskRecord);

                        log.info("物料申请流程回调信息：processCode={}, processInstanceId={}, eventType={}, activityName={}, type={}, result={}",
                                processCode, processInstanceId, eventType, activityName, type, result);

                        //获取审批实例详情
                        if ("发送数据到U9".equals(activityName) && "finish".equals(type) && "agree".equals(result)) {
                                log.info("发送数据到U9 START");
                            //发送本次的物料
                            rebuildNumber(dingTaskRecord);
                            initContext(dingTaskRecord.getCreateBy());
                            //添加评论，上传excel附件到钉盘
                            dingTalkService.addCommentWithExcelForWuliaoProcess(dingTaskRecord);
                            //同时创建 datasheet ，并且做关联
                            dingTalkService.createDataSheet(processInstanceId,dingTaskRecord);

                            List<InstanceEntity> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                                    dingTaskRecord.getPartInterationOidList(),InstanceEntity.class);
                            //物料申请流程只发当前物料，不发bom
//                            this.batchSendERP.batchSendERP(instanceList, Boolean.FALSE);

                            List<InstanceEntity> containFormingList = new ArrayList<>();
                            List<InstanceEntity> normalList = new ArrayList<>();

                            for (InstanceEntity instanceEntity : instanceList) {
                                String name = Optional.ofNullable(instanceEntity.getName()).orElse("");
                                String spec = Optional.ofNullable(instanceEntity.getExtensionContent())
                                        .map(e -> e.getString("cn_jwis_gg"))
                                        .orElse("");

                                boolean isForming = name.contains("筛选级") || name.contains("已成型")
                                        || spec.contains("筛选级") || spec.contains("已成型");

                                if (isForming) {
                                    containFormingList.add(instanceEntity);
                                } else {
                                    normalList.add(instanceEntity);
                                }
                            }

                            //  先发非“筛选级/已成型”的
                            for (InstanceEntity instanceEntity : normalList) {
                                partEntityRelease.release(null, instanceEntity.getOid(), "", 0);
                            }

                            try {
                                Thread.sleep(2000); // 2000ms = 2s
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt(); // 恢复中断标记
                                log.warn("等待发布间隔时被中断", e);
                            }
                            //  后发“筛选级/已成型”的
                            for (InstanceEntity instanceEntity : containFormingList) {
                                partEntityRelease.release(null, instanceEntity.getOid(), "", 0);
                            }
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        else if ("新增值集值".equals(activityName) &&"finish".equals(type) && "agree".equals(result)) {
                            log.info("进入-新增值集值节点处理");
                            // 流程结束后，临时编码的要重新生成编码
//                            rebuildNumber(dingTaskRecord);
                        }
                        else if ("登录U9系统或慧制造系统查看数据是否完成".equals(activityName) &&"finish".equals(type) && "agree".equals(result)) {
//                            setPartLifeStatus(dingTaskRecord);
//                            setPartLifeStatusByBom(dingTaskRecord);
                        } else if ("上传文件到PDM".equals(activityName) && "finish".equals(type) && "agree".equals(result)) {
                            initContext(dingTaskRecord.getCreateBy());
                            dingTalkService.uploadDINGFormObj(processInstanceId, dingTaskRecord);
                        }
                    }
                }
                else if(dingTalkProcessCode.equals(processCode)){
                    if("finish".equals(type)){
                        log.info(uuid + " bpms_instance_pdm 消息正常处理==>" + bodyStr);
                        dingTalkService.autoCompleteTask(bizData);
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                    log.warn(uuid + " bpms_instance_pdm 消息不是finish状态 直接返回响应==>" + bodyStr);
                    channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                    return;
                }
                else if (permProcessCode.equals(processCode) && !type.equals("start")
                        && (prodEnv && "PROC-5669439A-5455-4B31-A163-D720CF3F1F63".equals(permProcessCode) // 正式环境回调
                        || !prodEnv && "PROC-36550CC6-8BC8-46E0-8202-0F37A98EC950".equals(permProcessCode))) {// 正式环境备份回调
                    log.info("perm call==>" + JSONUtil.toJsonStr(bizData));

                    EntityFilter filter = new EntityFilter();
                    filter.setType(PermApplyEntity.TYPE);
                    filter.setFilter(Condition.where("oid").eq(processInstanceId));
                    PermApplyEntity record = (PermApplyEntity) CollectionUtil.getFirst(jwiCommonService.dynamicQuery(filter));
                    if(record == null)
                        throw new JWIException("未找到权限申请记录==>" + JSONUtil.toJsonStr(bizData));

                    record.setUpdateDate(System.currentTimeMillis());
                    UserDTO userDTO = new UserDTO();
                    userDTO.setAccount(record.getCreateBy());
                    userDTO.setTenantOid(record.getTenantOid());
//                    userDTO.setTenantOid("6deb5dde-aa39-46fb-962d-a5951f8fab5e");
                    SessionHelper.addCurrentUser(userDTO);
                    if ("terminate".equals(type)) {
                        record.setStatus("撤销");
                        commonAbilityHelper.doUpdate(record);
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }

                    if(("finish".equals(type) && "refuse".equals(result))){
                        record.setStatus("拒绝");
                        commonAbilityHelper.doUpdate(record);
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }

                    Config config = new Config(); config.protocol = "https"; config.regionId = "central"; String accessToken = permUseTest ? dingTalkService.getToken() : dingTalkService.getTokenNew();
                    GetProcessInstanceHeaders getProcessInstanceHeaders = new GetProcessInstanceHeaders();
                    getProcessInstanceHeaders.xAcsDingtalkAccessToken = accessToken;
                    Client client = new Client(config);
                    GetProcessInstanceRequest getProcessInstanceRequest = new GetProcessInstanceRequest().setProcessInstanceId(processInstanceId);
                    GetProcessInstanceResponse instanceResult = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new RuntimeOptions());
                    GetProcessInstanceResponseBody body = instanceResult.getBody();
                    log.info("result body==>" + JSONUtil.toJsonStr(body));
                    String status = body.getResult().getStatus();

                    if ("finish".equals(type) && "agree".equals(result) && "COMPLETED".equals(status)){
                        permApplyFinish(bizData, record, accessToken);
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                }
                else if(processCodeBOMRelease.equals(processCode)
                        &&(prodEnv && "PROC-B8731E81-A0AC-413A-A640-8FA3824AFABF".equals(processCodeBOMRelease) // 正式环境回调
                        || !prodEnv && "PROC-B8731E81-A0AC-413A-A640-8FA3824AFABF".equals(processCodeBOMRelease))) {// 测试环境回调
                    if("start".equals(type)){
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }

                    String activityName = bizData.getString("activityName");
                    dingTaskRecord = jwiCommonService.dynamicQueryOne(DingTaskRecord.TYPE, Condition.where("dingProcessInstanceId").eq(processInstanceId), DingTaskRecord.class);
                    log.info(" dingTaskRecord data {}", dingTaskRecord);
                    if (dingTaskRecord == null) {
                        log.warn("无钉钉流程记录，processInstanceId: {}", processInstanceId);
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                    if(eventType.equals("bpms_instance_change")) {

                        dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                        dingTaskRecord.setDingType(type);
                        dingTaskRecord.setResult(result);
                        jwiCommonService.update(dingTaskRecord);
                        if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                            customerCommonRepo.updateLifeStatus(PartIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(),
                                    "Design");
                            cancelPdmBomWorkFlow(dingTaskRecord);
                            clearCache(dingTaskRecord);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        if ("finish".equals(type) && "agree".equals(result)) {
                            String dingProcessName = dingTaskRecord.getDingProcessName();
                            String instanceId = processInstanceId;
                            log.info("流程已完成且为同意状态，流程名称：{}，InstanceId：{}", dingProcessName, instanceId);
//                            if (!"物料停用流程".equals(dingProcessName) && !"物料BOM及图纸发布流程-ECR".equals(dingProcessName)) {
                            if ("物料BOM及图纸发布流程".equals(dingProcessName)) {
                                log.info("进入物料发布流程处理逻辑");
                                signPDFAndReleaseByDingTalk(dingTaskRecord);
                                clearCache(dingTaskRecord);
                                log.info("清理缓存完成");
                            } else if("物料BOM及图纸发布流程-ECR".equals(dingProcessName)){
                                cancelPdmBomWorkFlow(dingTaskRecord);
                            }
                            // 消息确认
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            log.info("消息已确认，流程结束");
                            return;
                        }
                    }
                    else if (eventType.equals("bpms_task_change")) {
                        //获取审批实例详情
                        dingTaskRecord.setBusinessId(bizData.getString("businessId"));
                        dingTaskRecord.setDingType(type);
//                        dingTaskRecord.setResult(result);
                        jwiCommonService.update(dingTaskRecord);
                        String dingProcessName = dingTaskRecord.getDingProcessName();
                        String instanceId = processInstanceId;
                        if ("发送数据到U9".equals(activityName) && "finish".equals(type) && "agree".equals(result)) {
                            List<InstanceEntity> instancePartList = jwiCommonService.findByOid(PartIteration.TYPE,
                                    dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
                            List<InstanceEntity> instanceMCADList = jwiCommonService.findByOid(MCADIteration.TYPE,
                                    dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
                            List<InstanceEntity> instanceECADList = jwiCommonService.findByOid(ECADIteration.TYPE,
                                    dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
                            List<InstanceEntity> instanceList = new ArrayList<>();
                            instanceList.addAll(instancePartList);
                            instanceList.addAll(instanceMCADList);
                            instanceList.addAll(instanceECADList);
                            if ("物料停用流程".equals(dingProcessName)) {
                                log.info("物料停用流程-发送数据到U9-START");
                                log.info("进入物料停用流程处理逻辑");

                                initContext(dingTaskRecord.getCreateBy());
                                cancelPdmBomWorkFlow(dingTaskRecord);
                                log.info("已取消关联的PDM流程,流程ID：{}", dingTaskRecord.getPdmProcessOrderId());

                                dingTalkService.addCommentWithExcelForWuliaoProcess(dingTaskRecord);

                                log.info("开始进行物料停用相关逻辑");
                                List<String> documentIterationOidList = dingTaskRecord.getDocumentIterationOidList();
                                if (documentIterationOidList == null || documentIterationOidList.isEmpty()) {
                                    log.warn("物料停用流程中未获取到 DocumentIterationOidList，流程ID：{}", instanceId);
                                } else {
                                    // 设置失效物料的全局替代关系
                                    dingTalkService.setGlobalRel(processInstanceId, dingTaskRecord);


                                    for (String oid : documentIterationOidList) {
                                        log.info("处理失效物料，OID：{}", oid);
                                        ModelInfo modelInfo = new ModelInfo();
                                        modelInfo.setModelDefinition("JWIComponentsParts");
                                        modelInfo.setOid(oid);
                                        modelInfo.setType(PartIteration.TYPE);

                                        LifecycleAble lifecycleAble = partHelper.setStatus(modelInfo, "Deactivated");
                                        log.info("物料设置状态为 Deactivated，结果：{}", lifecycleAble != null ? "成功" : "失败");
                                    }


                                    for (InstanceEntity instanceEntity : instanceList) {
                                        ProcessOrder processOrder = processOrderHelper.findByOid(dingTaskRecord.getPdmProcessOrderId());
                                        log.info("当前的内部物料失效流程信息:{}", JSONUtil.toJsonStr(processOrder));
                                        partEntityRelease.release(processOrder, instanceEntity.getOid(), "", 0);
                                    }

                                }
                            }
                            else if ("物料BOM及图纸发布流程".equals(dingProcessName)) {
                                //其他发布和变更正常发布 物料信息
                                log.info("进入物料发布/变更处理逻辑");
                                initContext(dingTaskRecord.getCreateBy());
                                batchSendERP.batchSendERPWithBusinessType(instanceList, Boolean.FALSE, EntityReleaseFactory.BusinessType.item);
                            }

                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        if ("同步BOM数据".equals(activityName) && "finish".equals(type) && "agree".equals(result)) {
                            initContext(dingTaskRecord.getCreateBy());
                            List<InstanceEntity> instancePartList = jwiCommonService.findByOid(PartIteration.TYPE,
                                    dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
                            List<InstanceEntity> instanceMCADList = jwiCommonService.findByOid(MCADIteration.TYPE,
                                    dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
                            List<InstanceEntity> instanceECADList = jwiCommonService.findByOid(ECADIteration.TYPE,
                                    dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
                            List<InstanceEntity> instanceList = new ArrayList<>();
                            instanceList.addAll(instancePartList);
                            instanceList.addAll(instanceMCADList);
                            instanceList.addAll(instanceECADList);
                            batchSendERP.batchSendERPWithBusinessType(instanceList, Boolean.FALSE, EntityReleaseFactory.BusinessType.bom);
                            // 发送BOM到慧致造
                            try {
                                // 等待 15 秒
                                Thread.sleep(15000);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt(); // 恢复线程中断状态
                                log.error("等待执行 batchSendHZZ 时发生中断异常", e);
                            }
                            batchSendHZZ.batchSendHZZ(instanceList, Boolean.FALSE);
                        }
                    }

                }
                else if(processCodeSoftWareConfigCreate.equals(processCode)
                        &&(prodEnv && "PROC-0BD077A7-07A4-41DE-AEE1-30498DE1801E".equals(processCodeSoftWareConfigCreate) // 正式环境回调
                        || !prodEnv && "PROC-5DF156D7-98AE-4EC5-9C13-33B172042547".equals(processCodeSoftWareConfigCreate))) {
                    if("start".equals(type)){
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                    if(eventType.equals("bpms_instance_change")) {
                        if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                            log.info("软件配置项首次建库配置流程状态为 撤销或拒绝 instanceId:{}", processInstanceId);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        if ("finish".equals(type) && "agree".equals(result)) {
                            //1、根据钉钉的instanceId来查询对应的审批流程 2、在指定项目中创建文档，并上传相关附件 3、记录dingTaskRecord。后期可以通过流程来查看对应的钉钉流程
                            dingTalkHelper.createOrChangeDocForSoftWareConfig(bizData, Boolean.FALSE);
                            log.info("软件配置项首次建库配置流程状态为 同意 instanceId:{}", processInstanceId);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                    }
                }
                else if(processCodeSoftWareConfigChange.equals(processCode)
                        &&(prodEnv && "PROC-4A8A208E-C149-4741-B1A2-3C836CE62D1F".equals(processCodeSoftWareConfigChange) // 正式环境回调
                        || !prodEnv && "PROC-213AAB07-9BAC-47A7-921B-C2D783CB2431".equals(processCodeSoftWareConfigChange))) {
                    if("start".equals(type)){
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                    if(eventType.equals("bpms_instance_change")) {
                        if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                            log.info("软件配置项变更流程状态为 撤销或拒绝 instanceId:{}", processInstanceId);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        if ("finish".equals(type) && "agree".equals(result)) {

                            dingTalkHelper.createOrChangeDocForSoftWareConfig(bizData, Boolean.TRUE);
                            log.info("软件配置项变更流程状态为 同意 instanceId:{}", processInstanceId);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                    }

                }
                else if(processCodeFileOutSend.equals(processCode)
                        &&(prodEnv && "PROC-CF257670-B04F-40E2-B064-DD2BB52ECECA".equals(processCodeFileOutSend) // 正式环境回调
                        || !prodEnv && "PROC-84CA0DB2-3693-4AAA-8060-31D289E24866".equals(processCodeFileOutSend))) {
                    String activityName = bizData.getString("activityName");
                    String checkPerson = "发起人确认";
                    if("start".equals(type)){
                        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                        return;
                    }
                    if(eventType.equals("bpms_instance_change")) {
                        if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
                            log.info("文件外发流程状态为 撤销或拒绝 instanceId:{}", processInstanceId);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                        if ("finish".equals(type) && "agree".equals(result)) {
                            //1、文件外发流程处理逻辑
                            log.info("文件外发-信息化平台 同意 instanceId:{}", processInstanceId);
//                            dingTalkHelper.dealFileOutSend(bizData);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                    }
                    else if (eventType.equals("bpms_task_change")) {
                        log.info("activityName:{}", activityName);
                        if ((checkPerson.equals(activityName)) && "finish".equals(type) && "agree".equals(result) ) {
                            log.info("文件外发-信息化平台 同意 instanceId:{}", processInstanceId);
                            dingTalkHelper.dealFileOutSend(bizData);
                            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                            return;
                        }
                    }

                }

//                log.warn(uuid + " bpms_instance_pdm 配置processCode和消息中processCode不一致 配置processCode==>" + dingTalkProcessCode + " 消息中processCode==>" + processCode + " 直接返回响应==>" + bodyStr);
                log.warn("{} 无匹配processCode 消息中processCode==> {}", uuid, processCode);

                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            log.error(uuid + " bpms_instance_pdm messge 格式错误==>" + bodyStr);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            if(ObjectUtils.isNotEmpty(dingTaskRecord)) {
                saveException(e,dingTaskRecord);
            }
            try {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            } catch (Exception ioException) {
                log.error("响应失败==>" + e.getMessage(), e);
            }
        }
    }

    private boolean getIsAllCompleted(String processInstanceId, String activityId, String taskId) throws Exception {
        String token = dingTalkService.getTokenNew();
        List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks> dingTaskValues = dingTalkService.getDingTaskValues(processInstanceId, token);

        log.info("总任务数: {}", dingTaskValues.size());
        log.info("当前 taskId: {}, 当前 activityId: {}", taskId, activityId);

        // 筛选出同 activityId 且 taskId 不是当前 taskId 的任务
        List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks> filteredTasks = dingTaskValues.stream()
                .filter(task -> activityId.equals(task.getActivityId()))
                .filter(task -> !taskId.equals(String.valueOf(task.getTaskId())))
                .filter(task -> !"CANCELED".equalsIgnoreCase(task.getStatus()))
                .collect(Collectors.toList());

        log.info("同 activityId 且非当前 taskId 的任务数: {}", filteredTasks.size());

        for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks task : filteredTasks) {
            log.info("任务 taskId: {}, activityId: {}, status: {}", task.getTaskId(), task.getActivityId(), task.getStatus());
        }

        boolean isAllCompleted = filteredTasks.isEmpty() ||
                filteredTasks.stream().allMatch(task -> "COMPLETED".equals(task.getStatus()));

        log.info("是否全部完成: {}", isAllCompleted);
        return isAllCompleted;
    }


    private void setPartLifeStatusByBom(DingTaskRecord dingTaskRecord) {
        //此方法查询BOM有问题
        UserDTO byAccount = userHelper.findByAccount(dingTaskRecord.getCreateBy());
//        SessionHelper.addCurrentUser(byAccount);
        UserDTO currUser = new UserDTO();
        currUser.setAccount(dingTaskRecord.getCreateBy());
        currUser.setTenantOid(dingTaskRecord.getTenantOid());
        currUser.setOid(byAccount.getOid());
        SessionHelper.addCurrentUser(currUser);
        List<PartIteration> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                dingTaskRecord.getPartInterationOidList(),PartIteration.class);
        log.info("setPartLifeStatusByBom.instanceList:" + JSONUtil.toJsonStr(instanceList));
        for (PartIteration instanceEntity : instanceList) {
            String lifecycleStatus = "Released";
            String number = instanceEntity.getNumber();
            List<SimplePartBOMNode> bom = partHelper.findSimpleUseTree("",instanceEntity.getOid(),1);
            log.info("当前为物料{}，对应bom: {}", instanceEntity.getNumber(), JSONUtil.toJsonStr(bom));
            if (CollectionUtil.isNotEmpty(bom) && CollectionUtil.isNotEmpty(bom.get(0).getChildren())) {
                lifecycleStatus = "Design";
            }
            customerCommonRepo.updateLifeStatus(PartIteration.TYPE, Arrays.asList(instanceEntity.getOid()), lifecycleStatus);
            log.info("编码:" + number + "已更新状态:" + lifecycleStatus);
        }
    }
    private void setPartLifeStatus(DingTaskRecord dingTaskRecord) {
        //除了RM03 外的所有物料的状态要更新为设计中
        UserDTO byAccount = userHelper.findByAccount(dingTaskRecord.getCreateBy());
        SessionHelper.addCurrentUser(byAccount);
        List<PartIteration> instanceList = jwiCommonService.findByOid(PartIteration.TYPE,
                dingTaskRecord.getPartInterationOidList(),PartIteration.class);
        log.info("发送U9后节点数据为:" + JSONUtil.toJsonStr(instanceList));
        for (PartIteration instanceEntity : instanceList) {
            String number = instanceEntity.getNumber();
            if (!number.startsWith("RM03")) {
                customerCommonRepo.updateLifeStatus(PartIteration.TYPE, Arrays.asList(instanceEntity.getOid()), "Design");
                log.info("编码:" + number + "已更新");
            }
        }
    }

    private void adEcadSign(DingTaskRecord dingTaskRecord, String result, String type){
        log.info("ecadOid==>" + dingTaskRecord.getDocumentIterationOidList());
        dingTaskRecord.setResult(result);
        jwiCommonService.update(dingTaskRecord);
        if ("terminate".equals(type) || ("finish".equals(type) && "refuse".equals(result))) {
            customerCommonRepo.updateLifeStatus(ECADIteration.TYPE, dingTaskRecord.getDocumentIterationOidList(), "Design");
        }
        if ("finish".equals(type) && "agree".equals(result)) {
            DocSignWorkflowDTO docSignWorkflwoDTO = getDocSignWorkflowDTO(dingTaskRecord);
            JSONObject accountParamJson = docSignWorkflwoDTO.getAccountParams();
            JSONObject signPersonDTO = new JSONObject(), dataJson = new JSONObject();
            ecadSignCodeNameMap.entrySet().stream().forEach(acceptNoThrow(entry -> {
                String prcessNodeCode = entry.getKey();
                String signUserName = getOrElse(accountParamJson.getString(prcessNodeCode)).replaceAll(" ", ",");
                if("cs".equals(prcessNodeCode))
                    prcessNodeCode = "cn_jwis_countersigner";
                String signTime = accountParamJson.getString(prcessNodeCode + "_time");
                String date = sdf1.format(sdf.parse(signTime));
                dataJson.fluentPut(entry.getValue(), new JSONObject().fluentPut("name", signUserName).fluentPut("assignee", signUserName).fluentPut("date", date));
            }));
            UserDTO user = Optional.ofNullable(userHelper.findByAccount(dingTaskRecord.getCreateBy())).orElse(new UserDTO());
            String createDate = sdf1.format(new Date(dingTaskRecord.getCreateDate()));
            dataJson.fluentPut("提交审核", new JSONObject().fluentPut("name", user.getName()).fluentPut("assignee", user.getName()).fluentPut("date", createDate));

            JSONObject dwgSignWorkflowDTO = signPersonDTO.fluentPut("data", dataJson).fluentPut("tenantOid", docSignWorkflwoDTO.getTenantOid())
                    .fluentPut("processTemplateData", new JSONObject().fluentPut("signTemplateCode", "ecad-pdf签名模板A2").fluentPut("signTemplateOid", "").fluentPut("批准", "批准者")
                            .fluentPut("标准化", "标准化").fluentPut("会签", "会签者").fluentPut("校对", "校对者").fluentPut("提交审核", "提交者").fluentPut("审核", "审核者")
                            .fluentPut("版本节点", "版本占位").fluentPut("密级节点", "密级占位")
                            .fluentPut("tenantOid", docSignWorkflwoDTO.getTenantOid()).fluentPut("userAccount", dingTaskRecord.getCreateBy()))
                    .fluentPut("ecadOidList", dingTaskRecord.getDocumentIterationOidList());

            UserDTO currUser = new UserDTO();
            currUser.setAccount(dingTaskRecord.getCreateBy());
            currUser.setTenantOid(dingTaskRecord.getTenantOid());
            currUser.setOid(user.getOid());
            SessionHelper.addCurrentUser(currUser);
            log.info("adEcadSign.dwgSignWorkflowDTO---->>>{}", JSONUtil.toJsonStr(dwgSignWorkflowDTO));
            customerDocumentHelper.ecadDegSignWorkflow(dwgSignWorkflowDTO);
        }
    }

    @Autowired
    private BatchSendERP batchSendERP;

    @Autowired
    private InstanceHelper instanceHelper;

    /**
     * BOM发布流程PDF签名、发布
     * @param dingTaskRecord
     */
    private void signPDFAndReleaseByDingTalk(DingTaskRecord dingTaskRecord) {
        //撤销PDM内部工作流
        cancelPdmBomWorkFlow(dingTaskRecord);

        List<InstanceEntity> instancePartList = jwiCommonService.findByOid(PartIteration.TYPE,
                dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
        List<InstanceEntity> instanceMCADList = jwiCommonService.findByOid(MCADIteration.TYPE,
                dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
        List<InstanceEntity> instanceECADList = jwiCommonService.findByOid(ECADIteration.TYPE,
                dingTaskRecord.getDocumentIterationOidList(),InstanceEntity.class);
        if (null != instanceECADList && instanceECADList.size() > 0) {
            //处理ECAD类的签名 1、构造ecad签名参数
            JSONObject dwgSignWorkflowDTO = getEcadJsonObject(dingTaskRecord);
            customerDocumentHelper.ecadDegSignWorkflow(dwgSignWorkflowDTO);
        }
        if(null != instanceMCADList && instanceMCADList.size() > 0){
            //MCAD PDF签名
            DwgSignWorkflowDTO dwgSignWorkflowDTO = getPDFSignWorkflowDTO(dingTaskRecord);
            customerDocumentHelper.degSignByDingTalk(dwgSignWorkflowDTO);
        }

        log.info("instancePartList---->>{}", JSONUtil.toJsonStr(instancePartList));
        log.info("instanceMCADList---->>{}", JSONUtil.toJsonStr(instanceMCADList));
        log.info("instanceECADList---->>{}", JSONUtil.toJsonStr(instanceECADList));

        List<InstanceEntity> instanceList = new ArrayList<>();
        instanceList.addAll(instancePartList);
        instanceList.addAll(instanceMCADList);
        instanceList.addAll(instanceECADList);
        log.info("instanceList---->>{}", JSONUtil.toJsonStr(instanceList));
        try {
//            batchSendERP.batchSendERP(instanceList, Boolean.FALSE);
            batchSendERP.batchSendERPWithBusinessType(instanceList, Boolean.FALSE,
                    EntityReleaseFactory.BusinessType.mcad,
                    EntityReleaseFactory.BusinessType.ecad);

            //更新流程中的数据 设置1、入库时间、提交时间、流程业务ID、流程实例ID
            List<MCADIteration> mcadIterations = commonAbilityHelper.findDetailEntity(dingTaskRecord.getDocumentIterationOidList(), MCADIteration.TYPE).stream().map(item -> (MCADIteration)item).collect(Collectors.toList());
            for (MCADIteration mcadIteration : mcadIterations) {
                JSONObject extensionContent = mcadIteration.getExtensionContent();
                if (null == extensionContent) {
                    extensionContent = new JSONObject();
                }
                extensionContent.put("processInstanceId", dingTaskRecord.getDingProcessInstanceId());
                extensionContent.put("processBusinessId", dingTaskRecord.getBusinessId());
                extensionContent.put("storageTime", dingTaskRecord.getCreateDate());
                extensionContent.put("subTime", dingTaskRecord.getUpdateDate());
                mcadIteration.setExtensionContent(extensionContent);
            }
            commonAbilityHelper.doUpdate(mcadIterations);

            List<ECADIteration> ecadIterations = commonAbilityHelper.findDetailEntity(dingTaskRecord.getDocumentIterationOidList(), ECADIteration.TYPE).stream().map(item -> (ECADIteration)item).collect(Collectors.toList());
            for (ECADIteration ecadIteration : ecadIterations) {
                JSONObject extensionContent = ecadIteration.getExtensionContent();
                if (null == extensionContent) {
                    extensionContent = new JSONObject();
                }
                extensionContent.put("processInstanceId", dingTaskRecord.getDingProcessInstanceId());
                extensionContent.put("processBusinessId", dingTaskRecord.getBusinessId());
                extensionContent.put("storageTime", dingTaskRecord.getCreateDate());
                extensionContent.put("subTime", dingTaskRecord.getUpdateDate());
                ecadIteration.setExtensionContent(extensionContent);
            }
            commonAbilityHelper.doUpdate(ecadIterations);

            List<PartIteration> partIterations = commonAbilityHelper.findDetailEntity(dingTaskRecord.getDocumentIterationOidList(), PartIteration.TYPE).stream().map(item -> (PartIteration)item).collect(Collectors.toList());
            for (PartIteration partIteration : partIterations) {
                JSONObject extensionContent = partIteration.getExtensionContent();
                if (null == extensionContent) {
                    extensionContent = new JSONObject();
                }
                extensionContent.put("processInstanceId", dingTaskRecord.getDingProcessInstanceId());
                extensionContent.put("processBusinessId", dingTaskRecord.getBusinessId());
                extensionContent.put("storageTime", dingTaskRecord.getCreateDate());
                extensionContent.put("subTime", dingTaskRecord.getUpdateDate());
                partIteration.setExtensionContent(extensionContent);
            }
            commonAbilityHelper.doUpdate(partIterations);

            // 更新所有状态为 已发布
            if(CollectionUtil.isEmpty(instanceList)){
                return;
            }
            IntegrationMonitorRepo monitorRepo = (IntegrationMonitorRepo) SpringContextUtil.getBean("integrationMonitorNeo4jRepoImpl");
            String lifeCycleStatus = "Released";
            for(InstanceEntity instanceEntity : instanceList) {
                monitorRepo.updateStatusByOid(instanceEntity.getOid(), instanceEntity.getType(), lifeCycleStatus);
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
    }

    public JSONObject getEcadJsonObject(DingTaskRecord dingTaskRecord) {
        DocSignWorkflowDTO docSignWorkflwoDTO = getDocSignWorkflowDTO(dingTaskRecord);
        JSONObject accountParamJson = docSignWorkflwoDTO.getAccountParams();
        JSONObject signPersonDTO = new JSONObject(), dataJson = new JSONObject();
        ecadSignCodeNameMap.entrySet().stream().forEach(acceptNoThrow(entry -> {
            String prcessNodeCode = entry.getKey();
            String signUserName = getOrElse(accountParamJson.getString(prcessNodeCode)).replaceAll(" ", ",");
            if("cs".equals(prcessNodeCode))
                prcessNodeCode = "cn_jwis_countersigner";
            String signTime = accountParamJson.getString(prcessNodeCode + "_time");
            String date = sdf1.format(sdf.parse(signTime));
            dataJson.fluentPut(entry.getValue(), new JSONObject().fluentPut("name", signUserName).fluentPut("assignee", signUserName).fluentPut("date", date));
        }));
        UserDTO user = Optional.ofNullable(userHelper.findByAccount(dingTaskRecord.getCreateBy())).orElse(new UserDTO());
        String createDate = sdf1.format(new Date(dingTaskRecord.getCreateDate()));
        dataJson.fluentPut("提交审核", new JSONObject().fluentPut("name", user.getName()).fluentPut("assignee", user.getName()).fluentPut("date", createDate));

        JSONObject dwgSignWorkflowDTO = signPersonDTO.fluentPut("data", dataJson).fluentPut("tenantOid", docSignWorkflwoDTO.getTenantOid())
                .fluentPut("processTemplateData", new JSONObject().fluentPut("signTemplateCode", "ecad-pdf签名模板A2").fluentPut("signTemplateOid", "").fluentPut("批准", "批准者")
                        .fluentPut("标准化", "标准化").fluentPut("会签", "会签者").fluentPut("校对", "校对者").fluentPut("提交审核", "提交者").fluentPut("审核", "审核者")
                        .fluentPut("版本节点", "版本占位").fluentPut("密级节点", "密级占位")
                        .fluentPut("tenantOid", docSignWorkflwoDTO.getTenantOid()).fluentPut("userAccount", dingTaskRecord.getCreateBy()))
                .fluentPut("ecadOidList", dingTaskRecord.getDocumentIterationOidList())
                .fluentPut("processInstanceId",dingTaskRecord.getDingProcessInstanceId());

        UserDTO currUser = new UserDTO();
        currUser.setAccount(dingTaskRecord.getCreateBy());
        currUser.setTenantOid(dingTaskRecord.getTenantOid());
        currUser.setOid(user.getOid());
        SessionHelper.addCurrentUser(currUser);
        log.info("signPDFAndReleaseByDingTalk.dwgSignWorkflowDTO---->>>{}", JSONUtil.toJsonStr(dwgSignWorkflowDTO));
        return dwgSignWorkflowDTO;
    }

    private void cancelPdmBomWorkFlow(DingTaskRecord dingTaskRecord) {
        String owner = dingTaskRecord.getOwner();
        UserDTO byAccount = userHelper.findByAccount(owner);
        byAccount.setTenantOid(dingTaskRecord.getTenantOid());
        SessionHelper.addCurrentUser(byAccount);
        try {
            //取消pdm的流程
            ProcessOrder processOrder1 = processOrderHelper.cancelProcess(dingTaskRecord.getPdmProcessOrderId());
            log.info("PDM流程状态.processOrder1---->>>{}", JSONObject.toJSONString(processOrder1));
        } catch (Exception e) {
            log.error("processOrder1取消出错", e);
        }
    }

    private void signDocByDingTalk(DingTaskRecord dingTaskRecord) {
        DocSignWorkflowDTO docSignWorkflowDTO = getDocSignWorkflowDTO(dingTaskRecord);
        String owner = dingTaskRecord.getOwner();
        UserDTO byAccount = userHelper.findByAccount(owner);
        byAccount.setTenantOid(dingTaskRecord.getTenantOid());
        SessionHelper.addCurrentUser(byAccount);
        boolean b = documentHelper.docSignWorkflow(docSignWorkflowDTO);
        List<String> documentIterationOidList = dingTaskRecord.getDocumentIterationOidList();
        List<DocumentIteration> documentIterations = null;
        for (String documentIterationOid : documentIterationOidList)
            documentIterations = customerCommonRepo.queryFileDocumentIteration(documentIterationOid);
        List<InstanceEntity> instanceList = documentIterations.stream().map(doc -> JSON.toJavaObject(instanceHelper.findDetailByOid(doc.getOid(), doc.getType()), InstanceEntity.class)).collect(Collectors.toList());
        try {
            batchSendERP.batchSendERP(instanceList, Boolean.FALSE);
//        entityRelease(documentIterations);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        try {
//            releaseIDSDelegateIds(documentIterations);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("同步IDS发生错误-------->>>>>>{}", e.getMessage());
        }



    }

    private void releasePMS(DingTaskRecord dingTaskRecord) {
        for (String documentIterationOid : dingTaskRecord.getDocumentIterationOidList()){
            pmsRelease.pushDocumentInfoToPMS(documentIterationOid, "1");
        }
    }

    private void saveException(Exception e,DingTaskRecord dingTaskRecord) {
        String fullStackTrace = ExceptionUtils.getStackTrace(e);
        String[] lines = fullStackTrace.split("\n");
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < Math.min(lines.length, 200); i++) {
            sb.append(lines[i]).append("\n");
        }
        dingTaskRecord.setMessage(sb.toString());
        dingTaskRecord.setResult("error");
        jwiCommonService.update(dingTaskRecord);
    }

    @SneakyThrows
    public DwgSignWorkflowDTO getPDFSignWorkflowDTO(DingTaskRecord dingTaskRecord) {
        List<GetProcessInstanceResponseBodyResultTasks> dingWorkflowInfoList =
                getDingWorkflowInfo(dingTaskRecord.getDingProcessInstanceId()); //封装签名信息
        DwgSignWorkflowDTO dwgSignWorkflowDTO = new DwgSignWorkflowDTO();
        JSONObject templateData = JSONObject.parseObject("{\"signTemplateCode\":\"pdf签名模板\",\"提交审核\":\"提交者\",\"校对\":\"校对者\",\"审核\":\"审核者\",\"标准化\":\"标准化\",\"批准\":\"批准者\",\"会签\":\"会签者\"}");

        dwgSignWorkflowDTO.setData(new JSONObject().
                fluentPut("ReasedStatus", "UnderReview").
                fluentPut("operateType", "APPEND").
                fluentPut("pageNum", 3).
                fluentPut("signTemplateCode", "pdf签名模板").
                fluentPut("documentOidList", dingTaskRecord.getDocumentIterationOidList()));

        // 钉钉流程设置流程数据
        dwgSignWorkflowDTO.getData().put("processInstanceId", dingTaskRecord.getDingProcessInstanceId());
        dwgSignWorkflowDTO.getData().put("processBusinessId", dingTaskRecord.getBusinessId());
        dwgSignWorkflowDTO.getData().put("subTime", dingTaskRecord.getCreateDate());
        dwgSignWorkflowDTO.getData().put("storageTime", dingTaskRecord.getUpdateDate());

        JSONObject jsonObjectTextParams = new JSONObject();
        JSONObject jsonObjectAccountParams = new JSONObject();
        // 获取节点code为key 审批人名称清单为value数据  节点角色为key 审批人名称List为value
        HashMap<String, Set<String>> signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap = new HashMap<>();

        log.info("dingWorkflowInfoList : {}", JSONObject.toJSON(dingWorkflowInfoList));

        for (GetProcessInstanceResponseBodyResultTasks dingWorkflowInfo : dingWorkflowInfoList) {
            String ddActivityName_alsoInPdm = dingWorkflowInfo.getActivityName(), ddUserId = dingWorkflowInfo.getUserId();

            if (ddRoleNameAlsoInPdm_pdmSignCodeMap.containsKey(ddActivityName_alsoInPdm)) {
                String pdmSignCode = ddRoleNameAlsoInPdm_pdmSignCodeMap.get(ddActivityName_alsoInPdm);
                String ddActivityfinishTime = dingWorkflowInfo.getFinishTime().replace("-", "");
                String ddActivityFinishDate = ddActivityfinishTime.substring(0, 8);

                String userName = dingTalkService.getUserNameWeak(ddUserId, dingTalkService.getTokenNew());
                String ddUserInPdmUserName = "bpms_system".equals(ddUserId) || "NONE_USER".equals(userName) ? "" : StringUtils.isEmpty(userName) ? ddUserId : userName;
                signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.computeIfAbsent(pdmSignCode,
                        key -> new HashSet<>()).add(userName);
                Set<String> tempList = signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.get(pdmSignCode);
                tempList.add(ddUserInPdmUserName);
                jsonObjectAccountParams.put(pdmSignCode + "_time", ddActivityFinishDate);
                jsonObjectTextParams.put(pdmSignCode, ddActivityName_alsoInPdm);
            }
        }

        for(String pdmSignCode: ddRoleNameAlsoInPdm_pdmSignCodeMap.values()){
            if(signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.containsKey(pdmSignCode)) {
                Set<String> nameSet = signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.get(pdmSignCode);
                if ("cn_jwis_countersigner".equals(pdmSignCode)) {
                    pdmSignCode = "cs";
                }
                String value = StringUtils.join(nameSet.stream().limit(10).collect(Collectors.toList()), " ").replaceAll(" {2,}", " ");
                jsonObjectAccountParams.put(pdmSignCode, value);
                if (StrUtil.isEmpty(value)) {
                    jsonObjectAccountParams.put(pdmSignCode + "_time", "");
                }
            }
        }

        log.info("jsonObjectAccountParams : {}", JSONObject.toJSON(jsonObjectAccountParams));
        JSONObject accountParamJson = jsonObjectAccountParams;
        JSONObject dataJson = new JSONObject();
        pdfSignCodeNameMap.entrySet().stream().forEach(acceptNoThrow(entry -> {
            String prcessNodeCode = entry.getKey();
            String signUserName = getOrElse(accountParamJson.getString(prcessNodeCode)).replaceAll(" ", ",");
            if("cs".equals(prcessNodeCode))
                prcessNodeCode = "cn_jwis_countersigner";
            String signTime = accountParamJson.getString(prcessNodeCode + "_time");
            String date = sdf1.format(sdf.parse(signTime));
            dataJson.fluentPut(entry.getValue(), new JSONObject().fluentPut("name", signUserName).fluentPut("assignee", signUserName).fluentPut("date", date));
        }));
        //额外增加提交审核 的dataJson信息
        UserDTO byAccount = userHelper.findByAccount(dingTaskRecord.getCreateBy());
        String formatDate = DateUtil.format(DateUtil.date(dingTaskRecord.getCreateDate()), DatePattern.NORM_DATE_PATTERN.replace("-", "/"));
        dataJson.fluentPut("提交审核", new JSONObject().fluentPut("name", byAccount.getName()).fluentPut("assignee", byAccount.getName()).fluentPut("date", formatDate));

        dwgSignWorkflowDTO.setTenantOid(dingTaskRecord.getTenantOid());
        dwgSignWorkflowDTO.setProcessInstanceId(dingTaskRecord.getDingProcessInstanceId());
        dwgSignWorkflowDTO.setProcessTemplateData(templateData);
        dwgSignWorkflowDTO.getData().put("dataJson", dataJson);

        log.info("getPDFSignWorkflowDTO-dto-dwgSignWorkflowDTO==>" + JSONUtil.toJsonStr(dwgSignWorkflowDTO));

        return dwgSignWorkflowDTO;
    }

    @SneakyThrows
    public DocSignWorkflowDTO getDocSignWorkflowDTO(DingTaskRecord dingTaskRecord) {
        List<GetProcessInstanceResponseBodyResultTasks> dingWorkflowInfoList =
                getDingWorkflowInfo(dingTaskRecord.getDingProcessInstanceId()); //封装签名信息

        DocSignWorkflowDTO docSignWorkflowDTO = new DocSignWorkflowDTO();
        docSignWorkflowDTO.setData(new JSONObject().fluentPut("ReasedStatus", "UnderReview").fluentPut("operateType", "APPEND")
                .fluentPut("pageNum", 3).fluentPut("signTemplateCode", "签名模板").fluentPut("documentOidList", dingTaskRecord.getDocumentIterationOidList()));

        // 变更流程设置参数
        if(dingTaskRecord.getIsChange()) {
            docSignWorkflowDTO.getData().put("isChange",dingTaskRecord.getIsChange());
            docSignWorkflowDTO.getData().put("ecrOid",dingTaskRecord.getEcrOidList().stream().findAny().get());
        }
        // 钉钉流程设置流程数据
        docSignWorkflowDTO.getData().put("processInstanceId", dingTaskRecord.getDingProcessInstanceId());
        docSignWorkflowDTO.getData().put("processBusinessId", dingTaskRecord.getBusinessId());
        docSignWorkflowDTO.getData().put("subTime", dingTaskRecord.getCreateDate());
        docSignWorkflowDTO.getData().put("storageTime", dingTaskRecord.getUpdateDate());

        JSONObject jsonObjectTextParams = new JSONObject();
        JSONObject jsonObjectAccountParams = new JSONObject();
        // 获取节点code为key 审批人名称清单为value数据  节点角色为key 审批人名称List为value
        HashMap<String, Set<String>> signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap = new HashMap<>();

        log.info("dingWorkflowInfoList : {}", JSONObject.toJSON(dingWorkflowInfoList));
        boolean contains = dingWorkflowInfoList.stream()
                .anyMatch(task -> "会签".equals(task.getActivityName()) && !Objects.equals("bpms_system", task.getUserId()));
        //在这里处理没有 会签的情况（通过dingTaskRecord是否包含会签信息来判断）  1、要取出 批准的finishTime 2、将dingTask中的ids的会签信息 加入dingWorkflowInfoList中
        log.info("当前审批流程是否包含会签人员信息 : {}", JSONObject.toJSON(contains));
        if (!contains) {
            //如果不包含，去dingTaskRecord中查询是否包含ids会签信息
            List<String> idsAuditList = dingTaskRecord.getIdsAuditList();
            log.info("idsAuditList---->>>>{}", idsAuditList);
            if (null != idsAuditList && idsAuditList.size() > 0) {
                //1、要取出 批准的finishTime 2、将dingTask中的ids的会签信息 加入dingWorkflowInfoList中
                Optional<String> finishTime = dingWorkflowInfoList.stream()
                        .filter(task -> "批准".equals(task.getActivityName()))  // 过滤条件：activityName 等于 "批准"
                        .map(GetProcessInstanceResponseBodyResultTasks::getFinishTime)  // 提取 finishTime
                        .findFirst();  // 获取第一个符合条件的元素
                String time = finishTime.orElse(null);
                log.info("time---->>>>{}", time);
                for (String userId : idsAuditList) {
                    GetProcessInstanceResponseBodyResultTasks task = new GetProcessInstanceResponseBodyResultTasks();
                    task.setUserId(userId);
                    task.setFinishTime(time);
                    task.setActivityName("会签");
                    dingWorkflowInfoList.add(task);
                }

            }
            log.info("dingWorkflowInfoList增加ids会签人后 : {}", JSONObject.toJSON(dingWorkflowInfoList));
        }



        for (GetProcessInstanceResponseBodyResultTasks dingWorkflowInfo : dingWorkflowInfoList) {
            String ddActivityName_alsoInPdm = dingWorkflowInfo.getActivityName(), ddUserId = dingWorkflowInfo.getUserId();

            if (ddRoleNameAlsoInPdm_pdmSignCodeMap.containsKey(ddActivityName_alsoInPdm)) {
                String pdmSignCode = ddRoleNameAlsoInPdm_pdmSignCodeMap.get(ddActivityName_alsoInPdm);
                String ddActivityfinishTime = dingWorkflowInfo.getFinishTime().replace("-", "");
                String ddActivityFinishDate = ddActivityfinishTime.substring(0, 8);

                String userName = dingTalkService.getUserNameWeak(ddUserId, dingTalkService.getTokenNew());
                String ddUserInPdmUserName = "bpms_system".equals(ddUserId) || "NONE_USER".equals(userName) ? "" : StringUtils.isEmpty(userName) ? ddUserId : userName;
                signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.computeIfAbsent(pdmSignCode,
                        key -> new HashSet<>()).add(userName);
                Set<String> tempList = signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.get(pdmSignCode);
                tempList.add(ddUserInPdmUserName);
                jsonObjectAccountParams.put(pdmSignCode + "_time", ddActivityFinishDate);
                jsonObjectTextParams.put(pdmSignCode, ddActivityName_alsoInPdm);
            }
        }

        for(String pdmSignCode: ddRoleNameAlsoInPdm_pdmSignCodeMap.values()){
            if(signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.containsKey(pdmSignCode)) {
                Set<String> nameSet = signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.get(pdmSignCode);
                if ("cn_jwis_countersigner".equals(pdmSignCode)) {
                    pdmSignCode = "cs";
                }
                String value = StringUtils.join(nameSet.stream().limit(10).collect(Collectors.toList()), " ").replaceAll(" {2,}", " ");
                jsonObjectAccountParams.put(pdmSignCode, value);
                if (StrUtil.isEmpty(value)) {
                    jsonObjectAccountParams.put(pdmSignCode + "_time", "");
                }
            }
        }

        docSignWorkflowDTO.setTextParams(jsonObjectTextParams);
        docSignWorkflowDTO.setAccountParams(jsonObjectAccountParams);
        docSignWorkflowDTO.setTenantOid(dingTaskRecord.getTenantOid());
        docSignWorkflowDTO.setProcessInstanceId(dingTaskRecord.getDingProcessInstanceId());

        log.info("docSignWorkflowDTO==>" + JSONUtil.toJsonStr(docSignWorkflowDTO));

        return docSignWorkflowDTO;
    }

    public List<GetProcessInstanceResponseBodyResultTasks> getDingWorkflowInfo(String processInstanceId) throws Exception {
        String token = dingTalkService.getTokenNew();
        List<GetProcessInstanceResponseBodyResultTasks> list = new ArrayList<>();
        com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCall.createClient();
        com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();
        ProcessForecastHeaders processForecastHeaders = new ProcessForecastHeaders();

        getProcessInstanceHeaders.xAcsDingtalkAccessToken = token;
        processForecastHeaders.xAcsDingtalkAccessToken = token;
        com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest().setProcessInstanceId(processInstanceId);
        try {
            GetProcessInstanceResponse processInstanceWithOptions = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new RuntimeOptions());
            List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks> tasks = processInstanceWithOptions.getBody().getResult().getTasks(); //钉钉审批完成后获取所有节点信息

            String originatorUserId = processInstanceWithOptions.getBody().getResult().getOriginatorUserId(); //发起人用户id
            ProcessForecastRequest.ProcessForecastRequestFormComponentValues formComponentValues0 = new ProcessForecastRequest.ProcessForecastRequestFormComponentValues()
                    .setName("单选框")
                    .setValue("通过");
            ProcessForecastRequest processForecastRequest = new ProcessForecastRequest()
                    .setProcessCode(dingTalkprocessCodeJSDoc)
                    .setDeptId(1)
                    .setUserId(originatorUserId)
                    .setFormComponentValues(java.util.Arrays.asList(
                            formComponentValues0
                    ));
            List<WorkflowActivityRules> listWorkflowActivityRules = new ArrayList<>();
            String activityId, activityName;
            ProcessForecastResponse processForecastResponse = client.processForecastWithOptions(processForecastRequest, processForecastHeaders, new RuntimeOptions());
            List<ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules> workflowActivityRules = processForecastResponse.getBody().result.workflowActivityRules;

            for(ProcessForecastResponseBody.ProcessForecastResponseBodyResultWorkflowActivityRules workflowActivityRule:workflowActivityRules){
                WorkflowActivityRules workflowActivityRulesPo = new WorkflowActivityRules();
                activityId = workflowActivityRule.getActivityId();
                activityName = workflowActivityRule.getActivityName();
                workflowActivityRulesPo.setActivityId(activityId);
                workflowActivityRulesPo.setActivityName(activityName);
                listWorkflowActivityRules.add(workflowActivityRulesPo);
            }
            for(GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultTasks task:tasks){

                for(WorkflowActivityRules listWorkflowActivityRule:listWorkflowActivityRules){

                    if(task.activityId.equals(listWorkflowActivityRule.getActivityId())){
                        if("NONE".equals(task.getResult()) && task.getFinishTime() == null && "CANCELED".equals(task.status))
                            continue;

                        GetProcessInstanceResponseBodyResultTasks getProcessInstanceResponseBodyResultTasks = new GetProcessInstanceResponseBodyResultTasks();
                        getProcessInstanceResponseBodyResultTasks.setActivityName(listWorkflowActivityRule.getActivityName());
                        getProcessInstanceResponseBodyResultTasks.setFinishTime(task.getFinishTime());
                        getProcessInstanceResponseBodyResultTasks.setUserId(task.getUserId());
                        list.add(getProcessInstanceResponseBodyResultTasks);
                    }
                }
            }

            boolean hasHanShen = list.stream()
                    .anyMatch(task -> StrUtil.contains(task.getActivityName(), "函审"));
            //处理文档归档函审流程的两个分支条件中封面签名人信息，直接从钉钉OA流程表单中提取
            if (list.size() == 0 || hasHanShen) {
                list.clear();
                List<GetProcessInstanceResponseBodyResultTasks> listNew = new ArrayList<>();

                // 提取完成时间
                String finishTime = processInstanceWithOptions.getBody().getResult().getFinishTime();


                log.info("getDingWorkflowInfo.finishTime: {}", finishTime);
                // 从表单中直接提取信息
                List<GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultFormComponentValues> formComponentValues = processInstanceWithOptions.getBody().getResult().getFormComponentValues();

                // 需要处理的名称集合
                Set<String> namesToProcess = CollUtil.newHashSet("产保", "校对", "审核", "会签", "批准");

                // 遍历表单组件值
                formComponentValues.stream()
                        .filter(formComponentValue -> namesToProcess.contains(formComponentValue.getName()))
                        .forEach(formComponentValue -> {
                            String extValueArray = formComponentValue.getExtValue();

                            if (StrUtil.isNotBlank(extValueArray)) {
                                try {
                                    cn.hutool.json.JSONArray jsonArray = JSONUtil.parseArray(extValueArray);
                                    if (CollUtil.isNotEmpty(jsonArray)) {
                                        // 遍历 JSONArray 并处理每个项
                                        jsonArray.forEach(item -> {
                                            cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject) item;
                                            // 将每个 jsonObject 添加到新的任务列表中
                                            addTaskToList(jsonObject, formComponentValue.getName(), finishTime, listNew);
                                        });
                                    }
                                } catch (Exception e) {
                                    log.error("解析 extValueArray 时发生错误: {}", e.getMessage());
                                }
                            }
                        });

                // 更新原任务列表
                list = listNew;
            }


            list.stream().collect(Collectors.groupingBy(it -> it.getActivityName(), Collectors.collectingAndThen(Collectors.toList(), collectList -> {
                Map<Boolean, List<GetProcessInstanceResponseBodyResultTasks>> hasTimeNoTimeActivityMap = collectList.stream().collect(Collectors.partitioningBy(item -> item.getFinishTime() != null));
                List<GetProcessInstanceResponseBodyResultTasks> hasTimeActivityList = hasTimeNoTimeActivityMap.get(Boolean.TRUE);
                if(hasTimeActivityList != null && hasTimeActivityList.size() > 0){
                    GetProcessInstanceResponseBodyResultTasks hasTimeActivity = hasTimeActivityList.get(0);
                    hasTimeNoTimeActivityMap.get(Boolean.FALSE).forEach(noTimeActivity -> noTimeActivity.setFinishTime(hasTimeActivity.getFinishTime()));
                }
                return collectList;
            })));
        } catch (Exception _err) {
            log.error(_err.getMessage(), _err);
        }
        return list;
    }

    private void releaseIDSWithStatus(List<String> documentIterationOidList, long status) {
        List<DocumentIteration> documentIterations = null;
        for (String documentIterationOid : documentIterationOidList)
            documentIterations = customerCommonRepo.queryFileDocumentIteration(documentIterationOid);
        DocReleaseIDS docReleaseIDS = (DocReleaseIDS) SpringContextUtil.getBean("docReleaseIDS");
        log.info("要发布的docReleaseIDS集合----->>>>{}", JSONUtil.toJsonStr(docReleaseIDS));

        for (DocumentIteration entity : documentIterations) {
            if (DocumentIteration.TYPE.equals(entity.getType())) {
                docReleaseIDS.sendIDSWithStatus(entity.getOid(), status);
            }
        }
    }

    private void releaseIDS(List<DocumentIteration> documentIterationsList) {
        DocReleaseIDS docReleaseIDS = (DocReleaseIDS) SpringContextUtil.getBean("docReleaseIDS");
        log.info("要发布的docReleaseIDS集合----->>>>{}", JSONUtil.toJsonStr(docReleaseIDS));
        for(DocumentIteration entity : documentIterationsList){
            if(DocumentIteration.TYPE.equals(entity.getType())){
                docReleaseIDS.sendIDS(entity.getOid());
            }
        }
    }

    //发IDS
    public void releaseIDSDelegateIds(List<DocumentIteration> documentIterations) {

        try {
            //List<InstanceEntity> originInstanceEntityList = processOrderHelper.findBizObject(processOrder.getOid());
            ECAServiceImpl ecaService = (ECAServiceImpl) SpringContextUtil.getBean("ECAServiceImpl");

            List<DocumentIteration> instanceEntityList = new ArrayList<>();
            for (DocumentIteration documentIteration : documentIterations) {
                if (ECA.TYPE.equals(documentIteration.getType())) {
                    List<ChangeInfo> list = ecaService.findChangeInfo(documentIteration.getOid(), new ArrayList<>());
                    list.stream().forEach(changeInfo -> {
                        instanceEntityList.add(BeanUtil.copyProperties(changeInfo, new DocumentIteration()));
                    });
                } else {
                    instanceEntityList.add(documentIteration);
                }
            }
            // 评审对象为空则不处理
            if (CollectionUtil.isEmpty(instanceEntityList)) {
                log.info("ReleaseIDSDelegate  instanceEntity is empty! ");
                return;
            }
            // 发放数据,返回结果为此次需要发布的数据
//            releaseIDS(instanceEntityList);
        } catch (Exception e) {
            log.error("ReleaseIDSDelegate execute error==>", e);
            StringBuffer buffer = new StringBuffer();
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : stackTrace) {
                buffer.append(stackTraceElement.toString() + "\n");
            }
            // runtimeService.setVariable(execution.getId(), "releaseIDSError", "处理集成数据异常，请联系IT！\r\n" + buffer);
        }
    }

    @Value("${tenantOid:6deb5dde-aa39-46fb-962d-a5951f8fab5e}")
    private String tenantOid;

    @Value("${permApply.account:sys_admin}")
    private String permApplyAccount;

    @Value("${permApply.password:permApplyPassword!#%&(}")
    private String permApplyPassword;

    @Value("${jwis.platform.iam.service.gateway.url:http://172.16.8.67:5001}")
    private String gatewayUrl;

    @Value("${server.servlet.context-path:/pdm-standalone}")
    private String contextPath;

    @Autowired
    CommonAbilityHelper commonAbilityHelper;

    public void permApplyFinish(JSONObject bizData, PermApplyEntity record, String accessToken) throws Exception{

        OapiV2UserGetResponse.UserGetResponse rsp = dingTalkService.getUserDetail(record.getDdUserId(), accessToken);
        String mobile = rsp.getMobile();
        String jobNumber = rsp.getJobNumber();
        EntityFilter userFilter = new EntityFilter();
        userFilter.setType(User.TYPE);
        //                record.getDdUserName();
        //                userFilter.setFilter(Condition.where("name").eq(record.getDdUserName()));

        if(mobile != null && mobile.length() > 0)
            userFilter.setFilter(Condition.where("phone").contain(mobile.replaceFirst("86", "")));
        else if(jobNumber != null && jobNumber.length() > 0)
            userFilter.setFilter(Condition.where("number").eq(jobNumber));

        User user = CollectionUtil.getFirst(jwiCommonService.dynamicQuery(userFilter,User.class));

        if(user == null){
            log.info("根据钉钉用户==>" + JSONUtil.toJsonStr(rsp) + " 未找到pdm用户");
            return;
        }

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Tenantoid", tenantOid);
        headerMap.put("Appname", "pdm");
        cn.hutool.json.JSONObject json = HttpAssistant.post(gatewayUrl + contextPath + "/account-micro/authentication/login?account=" + permApplyAccount + "&password=" + permApplyPassword, new cn.hutool.json.JSONObject().set("account", permApplyAccount).set("password", permApplyPassword), cn.hutool.json.JSONObject.class, headerMap);
        log.info("请求token结果==>" + JSONUtil.toJsonStr(json));
        String token = json.getJSONObject("result").getStr("accesstoken");
        headerMap.put("Accesstoken", token);

        String permission = record.getPermission();
        Boolean isFK = "0".equals(permission), isTDCY = "1".equals(permission);
        cn.hutool.json.JSONArray roleArr = HttpAssistant.post(gatewayUrl + contextPath + "/account-micro/role/search/cp/keyword/page", new cn.hutool.json.JSONObject("{\n" +
                "  \"containerOid\": \"\",\n" +
                "  \"containerType\": \"Site\",\n" +
                "  \"keyword\": \"\",\n" +
                "  \"pageNum\": 1,\n" +
                "  \"pageSize\": 10000\n" +
                "}"), cn.hutool.json.JSONObject.class, headerMap).getJSONObject("result").getJSONArray("rows");
        cn.hutool.json.JSONObject fkRole = isFK ? (cn.hutool.json.JSONObject) roleArr.stream().filter(it -> "访客".equals(new cn.hutool.json.JSONObject(it).getStr("displayName"))).findFirst().orElse(null) : null;
        cn.hutool.json.JSONObject tdcyRole = isTDCY ? (cn.hutool.json.JSONObject) roleArr.stream().filter(it -> "团队成员".equals(new cn.hutool.json.JSONObject(it).getStr("displayName"))).findFirst().orElse(null) : null;

        safeWrapList(record.getFolderOid()).stream().forEach(folderOid -> {
            cn.hutool.json.JSONObject teamResult = HttpAssistant.get(gatewayUrl + contextPath + "/customer/pdm-folder/getTeamRole?folderOid=" + folderOid, cn.hutool.json.JSONObject.class, headerMap, null);
            String teamOid = teamResult.getJSONObject("result").getStr("oid");

            cn.hutool.json.JSONObject teamRoleResult = HttpAssistant.get(gatewayUrl + contextPath + "/container/team/fuzzyContent?teamOid=" + teamOid + "&searchKey=", cn.hutool.json.JSONObject.class, headerMap, null);

            cn.hutool.json.JSONArray teamRoleArr = teamRoleResult.getJSONArray("result");

            if(teamRoleArr != null && teamRoleArr.size() > 0){
                if(isFK){
                    Object fk = teamRoleArr.stream().filter(it -> "访客".equals(new cn.hutool.json.JSONObject(it).getStr("displayName"))).findFirst().orElse(null);
                    if(fk == null)
                        teamRoleArr.add(fkRole);
                }
                if(isTDCY){
                    Object tdcy = teamRoleArr.stream().filter(it -> "团队成员".equals(new cn.hutool.json.JSONObject(it).getStr("displayName"))).findFirst().orElse(null);
                    if(tdcy == null)
                        teamRoleArr.add(tdcyRole);
                }
            }else{
                teamRoleArr = new cn.hutool.json.JSONArray();
                if(isFK)
                    teamRoleArr.put(fkRole);
                if(isTDCY)
                    teamRoleArr.put(tdcyRole);
            }

            List<cn.hutool.json.JSONObject> teamRoleList = teamRoleArr.stream().map(it -> {
                cn.hutool.json.JSONObject itJson = (cn.hutool.json.JSONObject) it;
                if (itJson.getStr("sourceOid") != null) {
                    itJson.set("oid", itJson.getStr("sourceOid"));
                    itJson.remove("sourceOid");
                }
                return itJson;
            }).collect(Collectors.toList());

            cn.hutool.json.JSONObject bindRoleResult = HttpAssistant.post(gatewayUrl + contextPath + "/container/team/bindRole", new cn.hutool.json.JSONObject().set("containerOid", record.getContainerOid())
                    .set("teamOid", teamOid).set("teamRoleList", teamRoleList), cn.hutool.json.JSONObject.class, headerMap);

            cn.hutool.json.JSONObject afterAddTeamRoleResult = HttpAssistant.get(gatewayUrl + contextPath + "/container/team/fuzzyContent?teamOid=" + teamOid + "&searchKey=", cn.hutool.json.JSONObject.class, headerMap, null);

            cn.hutool.json.JSONObject bindTeam = isFK ? (cn.hutool.json.JSONObject) afterAddTeamRoleResult.getJSONArray("result").stream().filter(it -> "访客".equals(new cn.hutool.json.JSONObject(it).getStr("displayName"))).findFirst().orElse(null)
                    : (cn.hutool.json.JSONObject) afterAddTeamRoleResult.getJSONArray("result").stream().filter(it -> "团队成员".equals(new cn.hutool.json.JSONObject(it).getStr("displayName"))).findFirst().orElse(null);

            String bindTeamOid = bindTeam.getStr("oid");
            cn.hutool.json.JSONArray bindUserList = bindTeam.getJSONArray("users");
            if(bindUserList != null && bindUserList.size() > 0)
                bindUserList.add(user);
            else
                bindUserList = new cn.hutool.json.JSONArray().put(new cn.hutool.json.JSONObject(user));

            cn.hutool.json.JSONObject bindUserResult = HttpAssistant.post(gatewayUrl + contextPath + "/container/team/bindUser"
                    , new cn.hutool.json.JSONObject().set("containerOid", record.getFolderOid() != null && record.getFolderOid().size() > 0 ? record.getFolderOid().get(0) : record.getContainerOid())
                    .set("teamRoleOid", bindTeamOid).set("teamUserList", bindUserList), cn.hutool.json.JSONObject.class, headerMap);

            record.setStatus("已发布");
            record.setApprovedTime(System.currentTimeMillis());
            commonAbilityHelper.doUpdate(record);
        });

    }

    // 提取方法用于创建 RelatedFuzzyDTO
    private RelatedFuzzyDTO createRelatedFuzzyDTO(CollectionRule rule, String oid) {
        RelatedFuzzyDTO dto = new RelatedFuzzyDTO();
        dto.setRelationConstraint(
                "MM".equals(rule.getRelationConstraint()) ? RelationConstraint.MM :
                        "II".equals(rule.getRelationConstraint()) ? RelationConstraint.II :
                                RelationConstraint.MI
        );
        BeanUtils.copyProperties(rule, dto);
        dto.setMainObjectOid(oid);
        return dto;
    }

    // 提取方法用于创建 UserDTO
    private UserDTO createUserDTO(DingTaskRecord dingTaskRecord) {
        UserDTO dto = new UserDTO();
        dto.setAccount(dingTaskRecord.getCreateBy());
        dto.setTenantOid(dingTaskRecord.getTenantOid());
        return dto;
    }

    public void releaseDocForIds(DocumentIteration documentIteration, JSONArray teamContent, List<String> userDingIdList) {
        //构造 docSignWorkflowDTO
        DocSignWorkflowDTO docSignWorkflowDTO = getDocSignWorkflowDTOForIds(documentIteration,teamContent,userDingIdList);

        boolean b = customerDocumentHelper.docSignWorkflowForIds(docSignWorkflowDTO);

        //更新doc状态为已发布
        customerCommonRepo.updateLifeStatus(DocumentIteration.TYPE, Arrays.asList(documentIteration.getOid()), "Released");

        List<String> documentIterationOidList = Arrays.asList(documentIteration.getOid());
        List<DocumentIteration> documentIterations = null;
        for (String documentIterationOid : documentIterationOidList)
            documentIterations = customerCommonRepo.queryFileDocumentIteration(documentIterationOid);
        List<InstanceEntity> instanceList = documentIterations.stream().map(doc -> JSON.toJavaObject(instanceHelper.findDetailByOid(doc.getOid(), doc.getType()), InstanceEntity.class)).collect(Collectors.toList());
        try {
            batchSendERP.batchSendERP(instanceList, Boolean.FALSE);
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        try {
//            releaseIDSDelegateIds(documentIterations);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            log.error("同步IDS发生错误-------->>>>>>{}", e.getMessage());
        }

        //再次更新ids数据状态
//        releaseIDSWithStatus(Arrays.asList(documentIteration.getOid()), 0);
    }

    @SneakyThrows
    public DocSignWorkflowDTO getDocSignWorkflowDTOForIds(DocumentIteration documentIteration, JSONArray teamContent, List<String> userDingIdList) {
        List<GetProcessInstanceResponseBodyResultTasks> dingWorkflowInfoList = new ArrayList<>();
        String dingProcessInstanceId = OidGenerator.newOid();

        //使用 teamContent 构造新的审批参数
        log.info("teamContent----->>>{}", teamContent);

        ZonedDateTime now = ZonedDateTime.now();
        String formattedDateTime = now.format(DateTimeFormatter.ISO_INSTANT);
        for (int i = 0; i < teamContent.size(); i++) {
            JSONObject obj = teamContent.getJSONObject(i);
            String roleName = obj.getString("roleName");
            JSONArray users = obj.getJSONArray("users");

            for (int j = 0; j < users.size(); j++) {
                GetProcessInstanceResponseBodyResultTasks task = new GetProcessInstanceResponseBodyResultTasks();
                task.setActivityName(obj.getString("name"));
                task.setUserId(users.getString(j));
                task.setFinishTime(formattedDateTime);
                dingWorkflowInfoList.add(task);
            }
        }


        DocSignWorkflowDTO docSignWorkflowDTO = new DocSignWorkflowDTO();
        docSignWorkflowDTO.setData(new JSONObject()
                .fluentPut("checkInOut", "0")
                .fluentPut("isChange", Boolean.FALSE)
                .fluentPut("ReasedStatus", "UnderReview")
                .fluentPut("operateType", "APPEND")
                .fluentPut("pageNum", 3).fluentPut("signTemplateCode", "签名模板")
                .fluentPut("documentOidList", Arrays.asList(documentIteration.getOid())));

        // 钉钉流程设置流程数据
        long date = System.currentTimeMillis();
        docSignWorkflowDTO.getData().put("processInstanceId", dingProcessInstanceId);
        docSignWorkflowDTO.getData().put("processBusinessId", null);
        docSignWorkflowDTO.getData().put("subTime", date);
        docSignWorkflowDTO.getData().put("storageTime", date);

        JSONObject jsonObjectTextParams = new JSONObject();
        JSONObject jsonObjectAccountParams = new JSONObject();
        // 获取节点code为key 审批人名称清单为value数据  节点角色为key 审批人名称List为value
        HashMap<String, Set<String>> signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap = new HashMap<>();

        log.info("dingWorkflowInfoList : {}", JSONObject.toJSON(dingWorkflowInfoList));


        boolean contains = dingWorkflowInfoList.stream()
                .anyMatch(task -> "会签".equals(task.getActivityName()) && !Objects.equals("bpms_system", task.getUserId()));
        //在这里处理没有 会签的情况（通过dingTaskRecord是否包含会签信息来判断）  1、要取出 批准的finishTime 2、将dingTask中的ids的会签信息 加入dingWorkflowInfoList中
        log.info("当前审批流程是否包含会签人员信息 : {}", JSONObject.toJSON(contains));
        if (!contains) {
            //如果不包含，去dingTaskRecord中查询是否包含ids会签信息
            List<String> idsAuditList = userDingIdList;
            log.info("idsAuditList---->>>>{}", idsAuditList);
            if (null != idsAuditList && idsAuditList.size() > 0) {
                //1、要取出 批准的finishTime 2、将dingTask中的ids的会签信息 加入dingWorkflowInfoList中
                Optional<String> finishTime = dingWorkflowInfoList.stream()
                        .filter(task -> "批准".equals(task.getActivityName()))  // 过滤条件：activityName 等于 "批准"
                        .map(GetProcessInstanceResponseBodyResultTasks::getFinishTime)  // 提取 finishTime
                        .findFirst();  // 获取第一个符合条件的元素
                String time = finishTime.orElse(null);
                log.info("time---->>>>{}", time);
                for (String userId : idsAuditList) {
                    GetProcessInstanceResponseBodyResultTasks task = new GetProcessInstanceResponseBodyResultTasks();
                    task.setUserId(userId);
                    task.setFinishTime(time);
                    task.setActivityName("会签");
                    dingWorkflowInfoList.add(task);
                }

            }
            log.info("dingWorkflowInfoList增加ids会签人后 : {}", JSONObject.toJSON(dingWorkflowInfoList));
        }

        for (GetProcessInstanceResponseBodyResultTasks dingWorkflowInfo : dingWorkflowInfoList) {
            String ddActivityName_alsoInPdm = dingWorkflowInfo.getActivityName(), ddUserId = dingWorkflowInfo.getUserId();

            if (ddRoleNameAlsoInPdm_pdmSignCodeMap.containsKey(ddActivityName_alsoInPdm)) {
                String pdmSignCode = ddRoleNameAlsoInPdm_pdmSignCodeMap.get(ddActivityName_alsoInPdm);
                String ddActivityfinishTime = dingWorkflowInfo.getFinishTime().replace("-", "");
                String ddActivityFinishDate = ddActivityfinishTime.substring(0, 8);

                String userName = dingTalkService.getUserNameWeak(ddUserId, dingTalkService.getTokenNew());
                String ddUserInPdmUserName = "bpms_system".equals(ddUserId) || "NONE_USER".equals(userName) ? "" : StringUtils.isEmpty(userName) ? ddUserId : userName;
                signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.computeIfAbsent(pdmSignCode,
                        key -> new HashSet<>()).add(userName);
                Set<String> tempList = signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.get(pdmSignCode);
                tempList.add(ddUserInPdmUserName);
                jsonObjectAccountParams.put(pdmSignCode + "_time", ddActivityFinishDate);
                jsonObjectTextParams.put(pdmSignCode, ddActivityName_alsoInPdm);
            }
        }

        for(String pdmSignCode: ddRoleNameAlsoInPdm_pdmSignCodeMap.values()){
            if(signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.containsKey(pdmSignCode)) {
                Set<String> nameSet = signRoleCodeDdUserInPdmUserNameList_notInPdmWorkflowStartMap.get(pdmSignCode);
                if ("cn_jwis_countersigner".equals(pdmSignCode)) {
                    pdmSignCode = "cs";
                }
                String value = StringUtils.join(nameSet.stream().limit(10).collect(Collectors.toList()), " ").replaceAll(" {2,}", " ");
                jsonObjectAccountParams.put(pdmSignCode, value);
                if (StrUtil.isEmpty(value)) {
                    jsonObjectAccountParams.put(pdmSignCode + "_time", "");
                }
            }
        }

        docSignWorkflowDTO.setTextParams(jsonObjectTextParams);
        docSignWorkflowDTO.setAccountParams(jsonObjectAccountParams);
        docSignWorkflowDTO.setTenantOid(SessionHelper.getCurrentUser().getTenantOid());
        docSignWorkflowDTO.setProcessInstanceId(dingProcessInstanceId);

        log.info("docSignWorkflowDTO==>" + JSONUtil.toJsonStr(docSignWorkflowDTO));

        return docSignWorkflowDTO;
    }

    /**
     * 将任务对象添加到列表中
     *
     * @param jsonObject JSON 对象
     * @param activityName 活动名称
     * @param finishTime 完成时间
     * @param list 任务列表
     */
    private void addTaskToList(cn.hutool.json.JSONObject jsonObject, String activityName, String finishTime, List<GetProcessInstanceResponseBodyResultTasks> list) {
        String userId = jsonObject.getStr("emplId");
        if (StrUtil.isBlank(finishTime)) {
            ZonedDateTime now = ZonedDateTime.now();
            finishTime = now.format(DateTimeFormatter.ISO_INSTANT);
        }
        // 创建并设置 GetProcessInstanceResponseBodyResultTasks 对象
        GetProcessInstanceResponseBodyResultTasks task = new GetProcessInstanceResponseBodyResultTasks();
        task.setActivityName(activityName);  // 设置活动名称
        task.setUserId(userId);              // 设置用户 ID
        task.setFinishTime(finishTime);      // 设置完成时间

        // 将任务对象添加到列表中
        list.add(task);
    }
    private void initContext(String account){
        UserDTO byAccount = this.userHelper.findByAccount(account);
        if (byAccount == null) {
            throw new JWIException("该用户账号" + account + "不存在");
        }
        String tenantOid = commonService.getAloneTenantOid();
        byAccount.setTenantOid(tenantOid);
        byAccount.setOid(byAccount.getOid());
        byAccount.setAccount(account);
        this.authHelper.fillUserAuthInfo(byAccount);
        String accessToken = SessionHelper.getAccessToken();
        SessionHelper.setAccessToken(accessToken);
        SessionHelper.addCurrentUser(byAccount);
        SessionHelper.setAppId("pdm");
    }

}
